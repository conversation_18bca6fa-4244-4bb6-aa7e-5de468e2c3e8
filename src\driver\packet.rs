//! Kernel-mode packet handling utilities
//!
//! This module provides utilities for handling network packets in kernel mode,
//! including packet validation, parsing, and NDIS buffer management.

use super::*;
use crate::error::{TunError, TunResult};
use alloc::vec::Vec;

/// Kernel packet representation (simplified)
pub struct KernelPacket {
    /// Raw packet data
    pub data: Vec<u8>,
    /// Packet length
    pub length: usize,
    /// Associated buffer handle (placeholder)
    pub buffer_handle: Option<*mut core::ffi::c_void>,
}

impl KernelPacket {
    /// Create a new kernel packet from raw data
    pub fn new(data: Vec<u8>) -> Self {
        let length = data.len();
        Self {
            data,
            length,
            buffer_handle: None,
        }
    }

    /// Create a kernel packet from a slice
    pub fn from_slice(data: &[u8]) -> Self {
        Self::new(data.to_vec())
    }

    /// Validate the packet
    pub fn validate(&self) -> TunResult<()> {
        if self.data.is_empty() {
            return Err(TunError::InvalidPacketFormat("Empty packet".to_string()));
        }

        if self.data.len() > crate::packet::MAX_PACKET_SIZE {
            return Err(TunError::InvalidPacketFormat(
                "Packet too large".to_string(),
            ));
        }

        if self.length != self.data.len() {
            return Err(TunError::InvalidPacketFormat("Length mismatch".to_string()));
        }

        // Basic IP header validation
        self.validate_ip_header()
    }

    /// Validate IP header
    fn validate_ip_header(&self) -> TunResult<()> {
        if self.data.is_empty() {
            return Err(TunError::InvalidPacketFormat("No IP header".to_string()));
        }

        let version = (self.data[0] >> 4) & 0x0F;

        match version {
            4 => self.validate_ipv4_header(),
            6 => self.validate_ipv6_header(),
            _ => Err(TunError::InvalidPacketFormat(format!(
                "Invalid IP version: {}",
                version
            ))),
        }
    }

    /// Validate IPv4 header
    fn validate_ipv4_header(&self) -> TunResult<()> {
        if self.data.len() < 20 {
            return Err(TunError::InvalidPacketFormat(
                "IPv4 header too short".to_string(),
            ));
        }

        let header_length = ((self.data[0] & 0x0F) * 4) as usize;
        if header_length < 20 || header_length > self.data.len() {
            return Err(TunError::InvalidPacketFormat(
                "Invalid IPv4 header length".to_string(),
            ));
        }

        let total_length = u16::from_be_bytes([self.data[2], self.data[3]]) as usize;
        if total_length > self.data.len() {
            return Err(TunError::InvalidPacketFormat(
                "IPv4 total length exceeds packet size".to_string(),
            ));
        }

        Ok(())
    }

    /// Validate IPv6 header
    fn validate_ipv6_header(&self) -> TunResult<()> {
        if self.data.len() < 40 {
            return Err(TunError::InvalidPacketFormat(
                "IPv6 header too short".to_string(),
            ));
        }

        let payload_length = u16::from_be_bytes([self.data[4], self.data[5]]) as usize;
        if payload_length + 40 > self.data.len() {
            return Err(TunError::InvalidPacketFormat(
                "IPv6 payload length exceeds packet size".to_string(),
            ));
        }

        Ok(())
    }

    /// Get IP version
    pub fn ip_version(&self) -> Option<u8> {
        if self.data.is_empty() {
            None
        } else {
            Some((self.data[0] >> 4) & 0x0F)
        }
    }

    /// Get protocol field
    pub fn protocol(&self) -> Option<u8> {
        match self.ip_version()? {
            4 => {
                if self.data.len() >= 20 {
                    Some(self.data[9])
                } else {
                    None
                }
            }
            6 => {
                if self.data.len() >= 40 {
                    Some(self.data[6])
                } else {
                    None
                }
            }
            _ => None,
        }
    }

    /// Check if packet is IPv4
    pub fn is_ipv4(&self) -> bool {
        self.ip_version() == Some(4)
    }

    /// Check if packet is IPv6
    pub fn is_ipv6(&self) -> bool {
        self.ip_version() == Some(6)
    }
}

impl Drop for KernelPacket {
    fn drop(&mut self) {
        // Clean up associated buffer handle if any
        if let Some(_handle) = self.buffer_handle {
            // In real implementation would free the buffer
        }
    }
}

/// Packet pool for efficient packet allocation
pub struct PacketPool {
    /// Pool of pre-allocated packet buffers
    buffers: spin::Mutex<Vec<Vec<u8>>>,
    /// Maximum number of buffers in pool
    max_buffers: usize,
    /// Buffer size
    buffer_size: usize,
}

impl PacketPool {
    /// Create a new packet pool
    pub fn new(max_buffers: usize, buffer_size: usize) -> Self {
        Self {
            buffers: spin::Mutex::new(Vec::with_capacity(max_buffers)),
            max_buffers,
            buffer_size,
        }
    }

    /// Allocate a packet buffer from the pool
    pub fn allocate_buffer(&self) -> Vec<u8> {
        let mut buffers = self.buffers.lock();

        if let Some(mut buffer) = buffers.pop() {
            buffer.clear();
            buffer.reserve(self.buffer_size);
            buffer
        } else {
            Vec::with_capacity(self.buffer_size)
        }
    }

    /// Return a packet buffer to the pool
    pub fn return_buffer(&self, mut buffer: Vec<u8>) {
        let mut buffers = self.buffers.lock();

        if buffers.len() < self.max_buffers {
            buffer.clear();
            buffers.push(buffer);
        }
        // If pool is full, just drop the buffer
    }
}

/// Global packet pool instance
static PACKET_POOL: spin::Once<PacketPool> = spin::Once::new();

/// Initialize the global packet pool
pub fn init_packet_pool() {
    PACKET_POOL.call_once(|| PacketPool::new(256, crate::packet::MAX_PACKET_SIZE));
}

/// Get the global packet pool
pub fn get_packet_pool() -> &'static PacketPool {
    PACKET_POOL.get().expect("Packet pool not initialized")
}

/// Convert buffer to KernelPacket (simplified)
pub unsafe fn buffer_to_kernel_packet(buffer: *const u8, length: usize) -> TunResult<KernelPacket> {
    if buffer.is_null() || length == 0 {
        return Err(TunError::InvalidParameter("Invalid buffer".to_string()));
    }

    // Copy data from buffer
    let data_slice = core::slice::from_raw_parts(buffer, length);
    let packet_data = data_slice.to_vec();

    Ok(KernelPacket::new(packet_data))
}

/// Convert KernelPacket to buffer (simplified)
pub unsafe fn kernel_packet_to_buffer(packet: &KernelPacket) -> TunResult<*mut u8> {
    if packet.data.is_empty() {
        return Err(TunError::InvalidPacketFormat("Empty packet".to_string()));
    }

    // Allocate memory for packet data
    let packet_buffer =
        allocate_memory(packet.data.len()).ok_or(TunError::MemoryAllocationFailed)?;

    // Copy packet data
    core::ptr::copy_nonoverlapping(packet.data.as_ptr(), packet_buffer, packet.data.len());

    Ok(packet_buffer)
}
