# Windows TUN Driver

A high-performance Windows TUN (layer 3) virtual network adapter implementation written in Rust using the [windows-drivers-rs](https://github.com/microsoft/windows-drivers-rs) library.

## Features

- **Layer 3 Virtual Network Adapters**: Create TUN adapters that operate at the IP layer
- **IPv4 and IPv6 Support**: Full support for both IPv4 and IPv6 protocols
- **High Performance**: Efficient packet processing with minimal overhead
- **Multiple Adapter Support**: Create and manage multiple TUN adapters simultaneously
- **Comprehensive API**: Easy-to-use Rust API for userspace applications
- **NDIS Miniport Driver**: Kernel-mode driver based on Windows NDIS framework
- **Async Support**: Optional async/await support with Tokio integration

## Architecture

The project consists of two main components:

1. **Kernel Driver** (`src/driver/`): NDIS miniport driver that creates virtual network adapters
2. **Userspace Library** (`src/`): Rust library for creating and managing TUN adapters

### Communication Flow

```
Userspace Application
        ↕ (IOCTL)
    TUN Driver
        ↕ (NDIS)
   Windows Network Stack
```

## Requirements

- Windows 10/11 (x64)
- Rust 1.70+ with `nightly` toolchain for driver compilation
- Windows Driver Kit (WDK)
- Visual Studio Build Tools
- Administrator privileges for driver installation

## Installation

### 1. Install Dependencies

```bash
# Install Rust nightly toolchain
rustup toolchain install nightly
rustup default nightly

# Add required Rust targets
rustup target add x86_64-pc-windows-msvc
```

### 2. Build the Project

```bash
# Build userspace library
cargo build --release

# Build kernel driver (requires WDK)
cargo build --release --features driver --target x86_64-pc-windows-msvc
```

### 3. Install the Driver

1. Copy `tun_driver.sys` to a secure location
2. Install using Device Manager or `pnputil`:

```cmd
# Install driver (run as Administrator)
pnputil /add-driver driver.inf /install
```

## Usage

### Basic Example

```rust
use windows_tun::{AdapterConfig, AdapterManager, TunResult};
use std::net::Ipv4Addr;

#[tokio::main]
async fn main() -> TunResult<()> {
    // Initialize the library
    windows_tun::init()?;
    
    // Create adapter manager
    let manager = AdapterManager::new();
    
    // Configure TUN adapter
    let config = AdapterConfig {
        name: "MyTUN".to_string(),
        ipv4_address: Some((Ipv4Addr::new(10, 0, 0, 1), 24)),
        mtu: 1500,
        ..Default::default()
    };
    
    // Create adapter
    let adapter = manager.create_adapter(config)?;
    
    // Read packets
    while let Some(packet) = adapter.read_packet()? {
        println!("Received packet: {} bytes", packet.length);
        
        // Process packet...
        
        // Echo packet back
        adapter.write_packet(&packet)?;
    }
    
    Ok(())
}
```

### Async Example

```rust
use windows_tun::userspace::AsyncTunAdapter;

#[tokio::main]
async fn main() -> TunResult<()> {
    let adapter = create_adapter().await?;
    let mut async_adapter = AsyncTunAdapter::new(adapter);
    
    async_adapter.start().await?;
    
    while let Some(packet) = async_adapter.recv_packet().await {
        // Process packet asynchronously
        process_packet(&packet).await;
        
        // Send response
        async_adapter.send_packet(response_packet).await?;
    }
    
    Ok(())
}
```

## Examples

The `examples/` directory contains several usage examples:

- `simple_tun.rs`: Basic TUN adapter with packet echoing
- `packet_capture.rs`: Packet capture and analysis tool

Run examples with:

```bash
cargo run --example simple_tun
cargo run --example packet_capture
```

## API Reference

### Core Types

- `AdapterConfig`: Configuration for TUN adapters
- `TunAdapter`: Handle to a TUN adapter instance
- `AdapterManager`: Manager for multiple adapters
- `Packet`: Network packet representation
- `PacketBuffer`: Efficient packet buffer

### Key Methods

- `AdapterManager::create_adapter()`: Create a new TUN adapter
- `TunAdapter::read_packet()`: Read packet from adapter
- `TunAdapter::write_packet()`: Write packet to adapter
- `TunAdapter::set_ipv4_address()`: Configure IPv4 address
- `TunAdapter::stats()`: Get adapter statistics

## Driver Architecture

### NDIS Miniport Driver

The kernel driver implements an NDIS miniport driver with the following characteristics:

- **Media Type**: `NdisMediumIP` (Layer 3)
- **Physical Medium**: `NdisPhysicalMediumUnspecified`
- **Interface Type**: `IF_TYPE_TUNNEL`
- **Connection Type**: `NET_IF_CONNECTION_DEDICATED`

### IOCTL Interface

Communication between userspace and kernel uses IOCTL commands:

- `IOCTL_TUN_INITIALIZE`: Initialize adapter
- `IOCTL_TUN_READ_PACKET`: Read packet from kernel
- `IOCTL_TUN_WRITE_PACKET`: Write packet to kernel
- `IOCTL_TUN_SET_IPV4`: Configure IPv4 address
- `IOCTL_TUN_GET_STATS`: Get adapter statistics

## Performance

The driver is designed for high performance with:

- Zero-copy packet processing where possible
- Efficient memory management with packet pools
- Minimal kernel-userspace transitions
- Lock-free data structures for packet queues

## Security

Security considerations:

- Driver requires administrator privileges to install
- IOCTL interface validates all input parameters
- Memory allocations use tagged pool allocation
- Proper cleanup of resources on driver unload

## Troubleshooting

### Common Issues

1. **Driver Installation Fails**
   - Ensure running as Administrator
   - Check Windows version compatibility
   - Verify driver signature (disable test signing if needed)

2. **Adapter Creation Fails**
   - Check if driver is loaded: `sc query TunDriver`
   - Verify sufficient privileges
   - Check Windows Event Log for errors

3. **No Packets Received**
   - Verify adapter IP configuration
   - Check Windows routing table
   - Ensure firewall allows traffic

### Debug Mode

Enable debug logging:

```rust
env_logger::init();
log::set_max_level(log::LevelFilter::Debug);
```

View kernel debug output with DebugView or WinDbg.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under either of:

- Apache License, Version 2.0 ([LICENSE-APACHE](LICENSE-APACHE))
- MIT License ([LICENSE-MIT](LICENSE-MIT))

at your option.

## Acknowledgments

- [Microsoft windows-drivers-rs](https://github.com/microsoft/windows-drivers-rs) - Rust Windows driver development framework
- [WireGuard WinTun](https://github.com/WireGuard/wintun) - Reference TUN driver implementation
- [OpenVPN TAP-Windows](https://github.com/OpenVPN/tap-windows6) - TAP driver reference

## Disclaimer

This software is provided "as is" without warranty. Use at your own risk. The authors are not responsible for any damage caused by this software.

## Support

For questions and support:

- Open an issue on GitHub
- Check the documentation
- Review existing issues and discussions
