//! Simple TUN adapter example
//! 
//! This example demonstrates how to create a basic TUN adapter and handle packets.

use windows_tun::{AdapterConfig, AdapterManager, TunResult, Packet};
use std::net::Ipv4Addr;
use std::time::Duration;

#[tokio::main]
async fn main() -> TunResult<()> {
    // Initialize the library
    windows_tun::init()?;
    
    println!("Windows TUN Driver Example");
    println!("Version: {}", windows_tun::version());
    
    // Check if driver is available
    if !windows_tun::is_driver_available() {
        eprintln!("TUN driver is not available. Please install the driver first.");
        return Ok(());
    }
    
    // Create adapter manager
    let manager = AdapterManager::new();
    
    // Configure the TUN adapter
    let config = AdapterConfig {
        name: "ExampleTUN".to_string(),
        description: "Example TUN Adapter".to_string(),
        ipv4_address: Some((Ipv4Addr::new(10, 0, 0, 1), 24)),
        mtu: 1500,
        enable_ipv4: true,
        enable_ipv6: false,
        ..Default::default()
    };
    
    println!("Creating TUN adapter with configuration:");
    println!("  Name: {}", config.name);
    println!("  IPv4: {:?}", config.ipv4_address);
    println!("  MTU: {}", config.mtu);
    
    // Create the adapter
    let adapter = match manager.create_adapter(config) {
        Ok(adapter) => {
            println!("TUN adapter created successfully!");
            println!("  GUID: {}", adapter.guid());
            adapter
        }
        Err(e) => {
            eprintln!("Failed to create TUN adapter: {}", e);
            return Err(e);
        }
    };
    
    // Set up packet handling
    println!("\nStarting packet handling loop...");
    println!("Press Ctrl+C to stop");
    
    let mut packet_count = 0;
    let start_time = std::time::Instant::now();
    
    // Main packet processing loop
    loop {
        // Check for received packets
        match adapter.read_packet() {
            Ok(Some(packet)) => {
                packet_count += 1;
                
                println!("Received packet #{}: {} bytes", packet_count, packet.length);
                
                // Print packet information
                if let Ok(version) = packet.ip_version() {
                    println!("  IP version: {}", version);
                }
                
                if let Ok(src_ip) = packet.source_ip() {
                    println!("  Source IP: {}", src_ip);
                }
                
                if let Ok(dst_ip) = packet.destination_ip() {
                    println!("  Destination IP: {}", dst_ip);
                }
                
                if let Ok(protocol) = packet.protocol() {
                    println!("  Protocol: {:?}", protocol);
                }
                
                // Echo the packet back (simple ping responder)
                if let Err(e) = echo_packet(&adapter, &packet).await {
                    eprintln!("Failed to echo packet: {}", e);
                }
            }
            Ok(None) => {
                // No packet available, sleep briefly
                tokio::time::sleep(Duration::from_millis(10)).await;
            }
            Err(e) => {
                eprintln!("Error reading packet: {}", e);
                tokio::time::sleep(Duration::from_millis(100)).await;
            }
        }
        
        // Print statistics every 10 seconds
        if start_time.elapsed().as_secs() % 10 == 0 && packet_count > 0 {
            let stats = adapter.stats();
            println!("\nStatistics:");
            println!("  Packets received: {}", stats.packets_received);
            println!("  Packets sent: {}", stats.packets_sent);
            println!("  Bytes received: {}", stats.bytes_received);
            println!("  Bytes sent: {}", stats.bytes_sent);
            println!("  Packets dropped: {}", stats.packets_dropped);
            println!("  Errors: {}", stats.errors);
            println!();
        }
        
        // Check for shutdown signal
        if tokio::signal::ctrl_c().await.is_ok() {
            println!("\nShutdown signal received");
            break;
        }
    }
    
    println!("Processed {} packets", packet_count);
    println!("Shutting down...");
    
    Ok(())
}

/// Echo a packet back (simple ping responder)
async fn echo_packet(adapter: &windows_tun::TunAdapter, packet: &Packet) -> TunResult<()> {
    // For ICMP packets, we can create a simple echo response
    if let Ok(protocol) = packet.protocol() {
        if matches!(protocol, windows_tun::IpProtocol::Icmp) {
            // Create echo response packet
            let mut response_data = packet.data.clone();
            
            // For IPv4 ICMP, swap source and destination IPs
            if packet.data.len() >= 20 && response_data[0] >> 4 == 4 {
                // Swap IP addresses (bytes 12-15 with 16-19)
                for i in 0..4 {
                    response_data.swap(12 + i, 16 + i);
                }
                
                // Change ICMP type from Echo Request (8) to Echo Reply (0)
                if response_data.len() > 20 && response_data[20] == 8 {
                    response_data[20] = 0;
                    
                    // Recalculate ICMP checksum (simplified)
                    response_data[22] = 0;
                    response_data[23] = 0;
                    
                    let checksum = calculate_icmp_checksum(&response_data[20..]);
                    response_data[22] = (checksum >> 8) as u8;
                    response_data[23] = (checksum & 0xFF) as u8;
                }
                
                // Recalculate IP header checksum
                response_data[10] = 0;
                response_data[11] = 0;
                
                let ip_checksum = calculate_ip_checksum(&response_data[0..20]);
                response_data[10] = (ip_checksum >> 8) as u8;
                response_data[11] = (ip_checksum & 0xFF) as u8;
                
                // Send the response
                let response_packet = Packet::new(response_data);
                adapter.write_packet(&response_packet)?;
                
                println!("  -> Sent ICMP echo reply");
            }
        }
    }
    
    Ok(())
}

/// Calculate IP header checksum
fn calculate_ip_checksum(header: &[u8]) -> u16 {
    let mut sum: u32 = 0;
    
    // Sum all 16-bit words in the header
    for i in (0..header.len()).step_by(2) {
        if i + 1 < header.len() {
            let word = ((header[i] as u32) << 8) | (header[i + 1] as u32);
            sum += word;
        }
    }
    
    // Add carry bits
    while (sum >> 16) != 0 {
        sum = (sum & 0xFFFF) + (sum >> 16);
    }
    
    // One's complement
    !sum as u16
}

/// Calculate ICMP checksum
fn calculate_icmp_checksum(data: &[u8]) -> u16 {
    let mut sum: u32 = 0;
    
    // Sum all 16-bit words
    for i in (0..data.len()).step_by(2) {
        if i + 1 < data.len() {
            let word = ((data[i] as u32) << 8) | (data[i + 1] as u32);
            sum += word;
        } else {
            // Odd number of bytes, pad with zero
            let word = (data[i] as u32) << 8;
            sum += word;
        }
    }
    
    // Add carry bits
    while (sum >> 16) != 0 {
        sum = (sum & 0xFFFF) + (sum >> 16);
    }
    
    // One's complement
    !sum as u16
}
