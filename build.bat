@echo off
REM Windows TUN Driver Build Script
REM This script builds both the userspace library and kernel driver

echo Building Windows TUN Driver...
echo ==============================

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo Warning: Not running as administrator. Driver installation may fail.
    echo.
)

REM Set up environment
set RUST_BACKTRACE=1
set CARGO_TARGET_DIR=target

echo Step 1: Building userspace library...
cargo build --release --features userspace
if %errorLevel% neq 0 (
    echo Failed to build userspace library
    exit /b 1
)

echo Step 2: Building kernel driver...
REM Note: This requires WDK and proper setup
cargo build --release --features driver --target x86_64-pc-windows-msvc --bin tun_driver
if %errorLevel% neq 0 (
    echo Failed to build kernel driver
    echo Make sure Windows Driver Kit (WDK) is installed
    exit /b 1
)

echo Step 3: Building examples...
cargo build --release --examples
if %errorLevel% neq 0 (
    echo Failed to build examples
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Files created:
echo - target\release\windows_tun.dll (userspace library)
echo - target\x86_64-pc-windows-msvc\release\tun_driver.exe (kernel driver)
echo - target\release\examples\simple_tun.exe
echo - target\release\examples\packet_capture.exe
echo.
echo To install the driver:
echo 1. Copy tun_driver.exe to tun_driver.sys
echo 2. Run: pnputil /add-driver driver.inf /install
echo.
echo To test the library:
echo 1. Install the driver first
echo 2. Run: target\release\examples\simple_tun.exe
echo.

pause
