//! Windows TUN Driver Implementation
//!
//! This crate provides a Windows TUN (layer 3) virtual network adapter implementation
//! using Rust and the windows-drivers-rs library. It allows userspace applications
//! to create and manage virtual network adapters for tunneling and VPN applications.
//!
//! # Features
//!
//! - Layer 3 (IP) virtual network adapters
//! - IPv4 and IPv6 support
//! - High-performance packet I/O
//! - Multiple adapter support
//! - Comprehensive error handling
//! - Windows NDIS miniport driver backend
//!
//! # Example
//!
//! ```rust,no_run
//! use windows_tun::{AdapterConfig, AdapterManager, TunResult};
//! use std::net::Ipv4Addr;
//!
//! #[tokio::main]
//! async fn main() -> TunResult<()> {
//!     let manager = AdapterManager::new();
//!
//!     let config = AdapterConfig {
//!         name: "MyTUN".to_string(),
//!         ipv4_address: Some((Ipv4Addr::new(10, 0, 0, 1), 24)),
//!         ..Default::default()
//!     };
//!
//!     let adapter = manager.create_adapter(config)?;
//!
//!     // Read packets from the adapter
//!     while let Some(packet) = adapter.read_packet()? {
//!         println!("Received packet: {} bytes", packet.length);
//!
//!         // Echo the packet back
//!         adapter.write_packet(&packet)?;
//!     }
//!
//!     Ok(())
//! }
//! ```

#![cfg_attr(not(feature = "userspace"), no_std)]
#![cfg_attr(driver_build, no_main)]

// Re-export main types
pub use adapter::{AdapterConfig, AdapterManager, AdapterState, AdapterStats, TunAdapter};
pub use error::{TunError, TunResult};
pub use packet::{IpProtocol, Packet, PacketBuffer, MAX_PACKET_SIZE};

// Modules
pub mod adapter;
pub mod error;
pub mod packet;

// Driver-specific modules (only compiled for driver builds)
// Note: Driver module is currently disabled due to WDK dependencies
// #[cfg(feature = "driver")]
// pub mod driver;

// Userspace-specific functionality
#[cfg(feature = "userspace")]
pub mod userspace {
    //! Userspace utilities and async support

    use crate::{Packet, TunAdapter, TunResult};
    use std::sync::Arc;
    use tokio::sync::mpsc;

    /// Async TUN adapter wrapper
    pub struct AsyncTunAdapter {
        adapter: Arc<TunAdapter>,
        packet_sender: mpsc::UnboundedSender<Packet>,
        packet_receiver: mpsc::UnboundedReceiver<Packet>,
    }

    impl AsyncTunAdapter {
        /// Create a new async TUN adapter wrapper
        pub fn new(adapter: TunAdapter) -> Self {
            let (packet_sender, packet_receiver) = mpsc::unbounded_channel();

            Self {
                adapter: Arc::new(adapter),
                packet_sender,
                packet_receiver,
            }
        }

        /// Start the packet processing loop
        pub async fn start(&mut self) -> TunResult<()> {
            let adapter = self.adapter.clone();
            let sender = self.packet_sender.clone();

            // Spawn packet reading task
            tokio::spawn(async move {
                loop {
                    match adapter.read_packet() {
                        Ok(Some(packet)) => {
                            if sender.send(packet).is_err() {
                                break; // Channel closed
                            }
                        }
                        Ok(None) => {
                            // No packet available, yield
                            tokio::task::yield_now().await;
                        }
                        Err(_) => {
                            // Error reading packet, continue
                            tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
                        }
                    }
                }
            });

            Ok(())
        }

        /// Receive a packet asynchronously
        pub async fn recv_packet(&mut self) -> Option<Packet> {
            self.packet_receiver.recv().await
        }

        /// Send a packet asynchronously
        pub async fn send_packet(&self, packet: Packet) -> TunResult<()> {
            let adapter = self.adapter.clone();
            tokio::task::spawn_blocking(move || adapter.write_packet(&packet))
                .await
                .map_err(|e| crate::TunError::Unknown(format!("Task join error: {}", e)))?
        }

        /// Get the underlying adapter
        pub fn adapter(&self) -> &Arc<TunAdapter> {
            &self.adapter
        }
    }
}

/// Initialize the TUN driver library
pub fn init() -> TunResult<()> {
    #[cfg(feature = "userspace")]
    {
        env_logger::init();
        log::info!("Windows TUN driver library initialized");
    }

    Ok(())
}

/// Get library version
pub fn version() -> &'static str {
    env!("CARGO_PKG_VERSION")
}

/// Check if the TUN driver is available
pub fn is_driver_available() -> bool {
    #[cfg(windows)]
    {
        use windows::core::PCWSTR;
        use windows::Win32::Foundation::{CloseHandle, INVALID_HANDLE_VALUE};
        use windows::Win32::Storage::FileSystem::{CreateFileW, OPEN_EXISTING};

        let device_path = "\\\\.\\TunDriver";
        let device_path_wide: Vec<u16> = device_path
            .encode_utf16()
            .chain(std::iter::once(0))
            .collect();

        let handle = unsafe {
            CreateFileW(
                PCWSTR(device_path_wide.as_ptr()),
                0,
                windows::Win32::Storage::FileSystem::FILE_SHARE_READ,
                None,
                OPEN_EXISTING,
                windows::Win32::Storage::FileSystem::FILE_ATTRIBUTE_NORMAL,
                windows::Win32::Foundation::HANDLE::default(),
            )
        };

        match handle {
            Ok(h) if h != INVALID_HANDLE_VALUE => {
                unsafe {
                    let _ = CloseHandle(h);
                }
                true
            }
            _ => false,
        }
    }

    #[cfg(not(windows))]
    false
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version() {
        assert!(!version().is_empty());
    }

    #[test]
    fn test_init() {
        assert!(init().is_ok());
    }

    #[cfg(feature = "userspace")]
    #[tokio::test]
    async fn test_async_adapter() {
        use crate::adapter::AdapterConfig;
        use std::net::Ipv4Addr;

        let config = AdapterConfig {
            name: "TestTUN".to_string(),
            ipv4_address: Some((Ipv4Addr::new(192, 168, 1, 1), 24)),
            ..Default::default()
        };

        // This test would require the actual driver to be installed
        // For now, just test that the types compile correctly
        let _ = config;
    }

    #[test]
    fn test_packet_creation() {
        let data = vec![0x45, 0x00, 0x00, 0x1c]; // IPv4 header start
        let packet = Packet::from_slice(&data);
        assert_eq!(packet.length, 4);
        assert_eq!(packet.data, data);
    }

    #[test]
    fn test_packet_validation() {
        // Valid IPv4 packet (minimal)
        let ipv4_data = vec![
            0x45, 0x00, 0x00, 0x14, // Version, IHL, ToS, Total Length
            0x00, 0x00, 0x40, 0x00, // ID, Flags, Fragment Offset
            0x40, 0x01, 0x00, 0x00, // TTL, Protocol (ICMP), Checksum
            0x7f, 0x00, 0x00, 0x01, // Source IP (127.0.0.1)
            0x7f, 0x00, 0x00, 0x01, // Dest IP (127.0.0.1)
        ];

        let packet = Packet::from_slice(&ipv4_data);
        assert!(packet.validate().is_ok());
        assert_eq!(packet.ip_version().unwrap(), 4);
    }

    #[test]
    fn test_adapter_config() {
        use std::net::Ipv4Addr;

        let config = AdapterConfig {
            name: "TestAdapter".to_string(),
            ipv4_address: Some((Ipv4Addr::new(10, 0, 0, 1), 24)),
            mtu: 1500,
            ..Default::default()
        };

        assert_eq!(config.name, "TestAdapter");
        assert_eq!(config.mtu, 1500);
        assert!(config.enable_ipv4);
    }

    #[test]
    fn test_error_types() {
        let error = TunError::InvalidParameter("test".to_string());
        assert!(format!("{}", error).contains("Invalid parameter"));

        let error = TunError::AdapterNotFound("test".to_string());
        assert!(format!("{}", error).contains("Adapter not found"));
    }
}
