//! Kernel-mode adapter implementation
//! 
//! This module implements the kernel-side adapter that manages packet queues
//! and communication with userspace.

use super::*;
use crate::error::{Tun<PERSON><PERSON><PERSON>, TunR<PERSON><PERSON>};
use alloc::collections::VecDeque;
use alloc::vec::Vec;
use spin::Mutex;
use wdk_sys::*;
use windows::Wdk::Foundation::*;
use windows::Wdk::NetworkManagement::Ndis::*;

/// Maximum number of packets in receive queue
const MAX_RECEIVE_QUEUE_SIZE: usize = 256;

/// Maximum number of packets in send queue
const MAX_SEND_QUEUE_SIZE: usize = 256;

/// Kernel adapter instance
pub struct KernelAdapter {
    /// NDIS miniport handle
    pub ndis_handle: NDIS_HANDLE,
    /// MAC address of the adapter
    pub mac_address: [u8; 6],
    /// Queue for packets received from network stack (to be sent to userspace)
    receive_queue: Mutex<VecDeque<Vec<u8>>>,
    /// Queue for packets to be sent to network stack (from userspace)
    send_queue: Mutex<VecDeque<Vec<u8>>>,
    /// Adapter statistics
    stats: Mutex<AdapterStatistics>,
    /// Current packet filter
    packet_filter: Mutex<u32>,
    /// Current lookahead size
    lookahead_size: Mutex<u32>,
    /// Media connect state
    media_state: Mutex<NDIS_MEDIA_STATE>,
}

/// Adapter statistics for kernel mode
#[derive(Debug, Default)]
struct AdapterStatistics {
    frames_transmitted: u64,
    frames_received: u64,
    bytes_transmitted: u64,
    bytes_received: u64,
    transmit_errors: u64,
    receive_errors: u64,
    frames_dropped: u64,
}

impl KernelAdapter {
    /// Create a new kernel adapter
    pub fn new(ndis_handle: NDIS_HANDLE, mac_address: [u8; 6]) -> TunResult<Self> {
        Ok(Self {
            ndis_handle,
            mac_address,
            receive_queue: Mutex::new(VecDeque::with_capacity(MAX_RECEIVE_QUEUE_SIZE)),
            send_queue: Mutex::new(VecDeque::with_capacity(MAX_SEND_QUEUE_SIZE)),
            stats: Mutex::new(AdapterStatistics::default()),
            packet_filter: Mutex::new(0),
            lookahead_size: Mutex::new(1500),
            media_state: Mutex::new(NdisMediaStateConnected),
        })
    }
    
    /// Queue a packet received from the network stack
    pub fn queue_received_packet(&self, packet_data: &[u8]) -> TunResult<()> {
        let mut queue = self.receive_queue.lock();
        
        if queue.len() >= MAX_RECEIVE_QUEUE_SIZE {
            // Drop oldest packet if queue is full
            queue.pop_front();
            self.stats.lock().frames_dropped += 1;
        }
        
        queue.push_back(packet_data.to_vec());
        
        // Update statistics
        let mut stats = self.stats.lock();
        stats.frames_received += 1;
        stats.bytes_received += packet_data.len() as u64;
        
        Ok(())
    }
    
    /// Dequeue a packet to send to userspace
    pub fn dequeue_received_packet(&self) -> Option<Vec<u8>> {
        self.receive_queue.lock().pop_front()
    }
    
    /// Queue a packet from userspace to send to network stack
    pub fn queue_send_packet(&self, packet_data: &[u8]) -> TunResult<()> {
        let mut queue = self.send_queue.lock();
        
        if queue.len() >= MAX_SEND_QUEUE_SIZE {
            return Err(TunError::ResourceBusy("Send queue full".to_string()));
        }
        
        queue.push_back(packet_data.to_vec());
        
        // Update statistics
        let mut stats = self.stats.lock();
        stats.frames_transmitted += 1;
        stats.bytes_transmitted += packet_data.len() as u64;
        
        // Indicate packet to NDIS
        self.indicate_packet_to_ndis(packet_data)?;
        
        Ok(())
    }
    
    /// Indicate a packet to NDIS (send to network stack)
    fn indicate_packet_to_ndis(&self, packet_data: &[u8]) -> TunResult<()> {
        unsafe {
            // Allocate NET_BUFFER_LIST for the packet
            let nbl = self.allocate_net_buffer_list(packet_data)?;
            
            // Indicate the packet to NDIS
            NdisMIndicateReceiveNetBufferLists(
                self.ndis_handle,
                nbl,
                NDIS_DEFAULT_PORT_NUMBER,
                1, // NumberOfNetBufferLists
                NDIS_RECEIVE_FLAGS_DISPATCH_LEVEL,
            );
        }
        
        Ok(())
    }
    
    /// Allocate a NET_BUFFER_LIST for a packet
    unsafe fn allocate_net_buffer_list(&self, packet_data: &[u8]) -> TunResult<*mut NET_BUFFER_LIST> {
        // Allocate memory for the packet
        let packet_buffer = allocate_memory(packet_data.len())
            .ok_or(TunError::MemoryAllocationFailed)?;
        
        // Copy packet data
        core::ptr::copy_nonoverlapping(
            packet_data.as_ptr(),
            packet_buffer,
            packet_data.len(),
        );
        
        // Create MDL for the packet
        let mdl = IoAllocateMdl(
            packet_buffer as *mut _,
            packet_data.len() as u32,
            FALSE,
            FALSE,
            core::ptr::null_mut(),
        );
        
        if mdl.is_null() {
            free_memory(packet_buffer);
            return Err(TunError::MemoryAllocationFailed);
        }
        
        MmBuildMdlForNonPagedPool(mdl);
        
        // Allocate NET_BUFFER
        let net_buffer = NdisAllocateNetBuffer(
            core::ptr::null_mut(), // No pool handle - use default
            mdl,
            0, // DataOffset
            packet_data.len() as u32,
        );
        
        if net_buffer.is_null() {
            IoFreeMdl(mdl);
            free_memory(packet_buffer);
            return Err(TunError::MemoryAllocationFailed);
        }
        
        // Allocate NET_BUFFER_LIST
        let nbl = NdisAllocateNetBufferList(
            core::ptr::null_mut(), // No pool handle - use default
            0, // ContextSize
            0, // ContextBackFill
        );
        
        if nbl.is_null() {
            NdisFreeNetBuffer(net_buffer);
            IoFreeMdl(mdl);
            free_memory(packet_buffer);
            return Err(TunError::MemoryAllocationFailed);
        }
        
        // Link NET_BUFFER to NET_BUFFER_LIST
        (*nbl).FirstNetBuffer = net_buffer;
        (*net_buffer).Next = core::ptr::null_mut();
        
        Ok(nbl)
    }
    
    /// Get adapter statistics
    pub fn get_statistics(&self) -> AdapterStatistics {
        *self.stats.lock()
    }
    
    /// Set packet filter
    pub fn set_packet_filter(&self, filter: u32) {
        *self.packet_filter.lock() = filter;
    }
    
    /// Get packet filter
    pub fn get_packet_filter(&self) -> u32 {
        *self.packet_filter.lock()
    }
    
    /// Set lookahead size
    pub fn set_lookahead_size(&self, size: u32) {
        *self.lookahead_size.lock() = size;
    }
    
    /// Get lookahead size
    pub fn get_lookahead_size(&self) -> u32 {
        *self.lookahead_size.lock()
    }
    
    /// Set media state
    pub fn set_media_state(&self, state: NDIS_MEDIA_STATE) {
        let mut current_state = self.media_state.lock();
        if *current_state != state {
            *current_state = state;
            
            // Indicate media state change to NDIS
            unsafe {
                let mut status_indication = NDIS_STATUS_INDICATION::default();
                status_indication.Header.Type = NDIS_OBJECT_TYPE_STATUS_INDICATION;
                status_indication.Header.Size = core::mem::size_of::<NDIS_STATUS_INDICATION>() as u16;
                status_indication.Header.Revision = NDIS_STATUS_INDICATION_REVISION_1;
                
                status_indication.SourceHandle = self.ndis_handle;
                status_indication.StatusCode = NDIS_STATUS_MEDIA_CONNECT;
                status_indication.StatusBuffer = &state as *const _ as *mut _;
                status_indication.StatusBufferSize = core::mem::size_of::<NDIS_MEDIA_STATE>() as u32;
                
                NdisMIndicateStatusEx(self.ndis_handle, &status_indication);
            }
        }
    }
    
    /// Get media state
    pub fn get_media_state(&self) -> NDIS_MEDIA_STATE {
        *self.media_state.lock()
    }
    
    /// Process pending send packets
    pub fn process_send_queue(&self) -> TunResult<()> {
        let mut queue = self.send_queue.lock();
        
        while let Some(packet_data) = queue.pop_front() {
            // In a real implementation, you would send these packets
            // For now, we just drop them after processing
            log_debug(&format!("Processing send packet: {} bytes", packet_data.len()));
        }
        
        Ok(())
    }
    
    /// Reset adapter statistics
    pub fn reset_statistics(&self) {
        *self.stats.lock() = AdapterStatistics::default();
    }
    
    /// Check if receive queue has packets
    pub fn has_received_packets(&self) -> bool {
        !self.receive_queue.lock().is_empty()
    }
    
    /// Get receive queue length
    pub fn receive_queue_length(&self) -> usize {
        self.receive_queue.lock().len()
    }
    
    /// Get send queue length
    pub fn send_queue_length(&self) -> usize {
        self.send_queue.lock().len()
    }
}

impl Drop for KernelAdapter {
    fn drop(&mut self) {
        // Clean up any remaining packets in queues
        self.receive_queue.lock().clear();
        self.send_queue.lock().clear();
        
        log_debug("Kernel adapter dropped");
    }
}
