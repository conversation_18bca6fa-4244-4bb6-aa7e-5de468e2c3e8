//! Windows TUN Driver Kernel Module
//!
//! This module contains the kernel-mode driver implementation for the Windows TUN adapter.
//! It implements an NDIS miniport driver that creates virtual network adapters.

#![cfg(feature = "driver")]

extern crate alloc;
use alloc::vec::Vec;
use wdk::*;
use wdk_sys::*;

pub mod adapter;
pub mod ioctl;
pub mod miniport;
pub mod packet;

use crate::error::{TunError, TunResult};

/// Driver global state
pub struct DriverGlobals {
    /// Driver handle
    pub driver_handle: *mut DRIVER_OBJECT,
    /// List of active adapters
    pub adapters: spin::Mutex<Vec<adapter::KernelAdapter>>,
    /// Driver version
    pub version: u32,
}

static mut DRIVER_GLOBALS: Option<DriverGlobals> = None;

/// Get driver globals (unsafe - must be called from driver context)
pub unsafe fn get_driver_globals() -> &'static mut DriverGlobals {
    DRIVER_GLOBALS.as_mut().expect("Driver not initialized")
}

/// Initialize driver globals
pub unsafe fn init_driver_globals(driver_handle: *mut DRIVER_OBJECT) -> TunResult<()> {
    DRIVER_GLOBALS = Some(DriverGlobals {
        driver_handle,
        adapters: spin::Mutex::new(Vec::new()),
        version: 0x00010000, // Version 1.0
    });

    Ok(())
}

/// Driver entry point
#[cfg(driver_build)]
#[no_mangle]
pub extern "system" fn DriverEntry(
    driver_object: *mut DRIVER_OBJECT,
    registry_path: *const UNICODE_STRING,
) -> NTSTATUS {
    // Initialize allocator for no_std environment
    #[global_allocator]
    static ALLOCATOR: linked_list_allocator::LockedHeap =
        linked_list_allocator::LockedHeap::empty();

    unsafe {
        // Initialize heap
        let heap_start = 0x_4444_4444_0000;
        let heap_size = 100 * 1024; // 100 KB
        ALLOCATOR.lock().init(heap_start, heap_size);

        // Register NDIS miniport driver
        match register_miniport_driver(driver_object, registry_path) {
            Ok(_) => STATUS_SUCCESS,
            Err(_) => STATUS_UNSUCCESSFUL,
        }
    }
}

/// Register a simple driver (placeholder for NDIS miniport)
unsafe fn register_miniport_driver(
    driver_object: *mut DRIVER_OBJECT,
    _registry_path: *const UNICODE_STRING,
) -> TunResult<()> {
    // For now, just initialize driver globals
    init_driver_globals(driver_object)?;
    log_debug("Simple TUN driver registered");
    Ok(())
}

/// Driver unload routine
unsafe extern "system" fn driver_unload(_driver_object: *mut DRIVER_OBJECT) {
    log_debug("TUN driver unloaded");
}

/// Allocate memory in kernel space (placeholder)
pub fn allocate_memory(size: usize) -> Option<*mut u8> {
    // For now, use a simple allocation strategy
    // In a real implementation, this would use ExAllocatePool2
    if size > 0 && size < 1024 * 1024 {
        // Placeholder - would allocate from kernel pool
        Some(core::ptr::null_mut())
    } else {
        None
    }
}

/// Free memory in kernel space (placeholder)
pub unsafe fn free_memory(_ptr: *mut u8) {
    // Placeholder - would free kernel pool memory
}

/// Log a message to the kernel debugger (placeholder)
pub fn log_debug(_message: &str) {
    // Placeholder - would use DbgPrint in real implementation
}

/// Log an error message (placeholder)
pub fn log_error(_message: &str) {
    // Placeholder - would use DbgPrint in real implementation
}

/// Convert NTSTATUS to TunResult (placeholder)
pub fn ntstatus_to_result(status: i32) -> TunResult<()> {
    if status == 0 {
        Ok(())
    } else {
        Err(crate::error::ntstatus_to_error(status, "kernel operation"))
    }
}

// Remove duplicate allocator and panic handler - they're in main.rs
