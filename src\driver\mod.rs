//! Windows TUN Driver Kernel Module
//!
//! This module contains the kernel-mode driver implementation for the Windows TUN adapter.
//! It implements an NDIS miniport driver that creates virtual network adapters.

#![cfg(feature = "driver")]

use wdk_sys::*;
use windows::Wdk::Foundation::*;
use windows::Wdk::NetworkManagement::Ndis::*;
use windows::Win32::Foundation::*;

pub mod adapter;
pub mod ioctl;
pub mod miniport;
pub mod packet;

use crate::error::{TunError, TunResult};

/// Driver global state
pub struct DriverGlobals {
    /// NDIS driver handle
    pub ndis_handle: NDIS_HANDLE,
    /// List of active adapters
    pub adapters: spin::Mutex<alloc::vec::Vec<adapter::KernelAdapter>>,
    /// Driver version
    pub version: u32,
}

static mut DRIVER_GLOBALS: Option<DriverGlobals> = None;

/// Get driver globals (unsafe - must be called from driver context)
pub unsafe fn get_driver_globals() -> &'static mut DriverGlobals {
    DRIVER_GLOBALS.as_mut().expect("Driver not initialized")
}

/// Initialize driver globals
pub unsafe fn init_driver_globals(ndis_handle: NDIS_HANDLE) -> TunResult<()> {
    DRIVER_GLOBALS = Some(DriverGlobals {
        ndis_handle,
        adapters: spin::Mutex::new(alloc::vec::Vec::new()),
        version: 0x00010000, // Version 1.0
    });

    Ok(())
}

/// Driver entry point
#[cfg(driver_build)]
#[no_mangle]
pub extern "system" fn DriverEntry(
    driver_object: *mut DRIVER_OBJECT,
    registry_path: *const UNICODE_STRING,
) -> NTSTATUS {
    // Initialize allocator for no_std environment
    #[global_allocator]
    static ALLOCATOR: linked_list_allocator::LockedHeap =
        linked_list_allocator::LockedHeap::empty();

    unsafe {
        // Initialize heap
        let heap_start = 0x_4444_4444_0000;
        let heap_size = 100 * 1024; // 100 KB
        ALLOCATOR.lock().init(heap_start, heap_size);

        // Register NDIS miniport driver
        match register_miniport_driver(driver_object, registry_path) {
            Ok(_) => STATUS_SUCCESS,
            Err(_) => STATUS_UNSUCCESSFUL,
        }
    }
}

/// Register the NDIS miniport driver
unsafe fn register_miniport_driver(
    driver_object: *mut DRIVER_OBJECT,
    registry_path: *const UNICODE_STRING,
) -> TunResult<()> {
    let mut driver_characteristics = NDIS_MINIPORT_DRIVER_CHARACTERISTICS::default();

    // Set driver characteristics
    driver_characteristics.Header.Type = NDIS_OBJECT_TYPE_MINIPORT_DRIVER_CHARACTERISTICS;
    driver_characteristics.Header.Size =
        core::mem::size_of::<NDIS_MINIPORT_DRIVER_CHARACTERISTICS>() as u16;
    driver_characteristics.Header.Revision = NDIS_MINIPORT_DRIVER_CHARACTERISTICS_REVISION_3;

    driver_characteristics.MajorNdisVersion = NDIS_MINIPORT_MAJOR_VERSION as u8;
    driver_characteristics.MinorNdisVersion = NDIS_MINIPORT_MINOR_VERSION as u8;
    driver_characteristics.MajorDriverVersion = 1;
    driver_characteristics.MinorDriverVersion = 0;

    // Set callback functions
    driver_characteristics.InitializeHandlerEx = Some(miniport::initialize_handler);
    driver_characteristics.HaltHandlerEx = Some(miniport::halt_handler);
    driver_characteristics.UnloadHandler = Some(miniport::unload_handler);
    driver_characteristics.PauseHandler = Some(miniport::pause_handler);
    driver_characteristics.RestartHandler = Some(miniport::restart_handler);
    driver_characteristics.OidRequestHandler = Some(miniport::oid_request_handler);
    driver_characteristics.SendNetBufferListsHandler =
        Some(miniport::send_net_buffer_lists_handler);
    driver_characteristics.ReturnNetBufferListsHandler =
        Some(miniport::return_net_buffer_lists_handler);
    driver_characteristics.CancelSendHandler = Some(miniport::cancel_send_handler);
    driver_characteristics.CheckForHangHandlerEx = Some(miniport::check_for_hang_handler);
    driver_characteristics.ResetHandlerEx = Some(miniport::reset_handler);
    driver_characteristics.DevicePnPEventNotifyHandler =
        Some(miniport::device_pnp_event_notify_handler);
    driver_characteristics.ShutdownHandlerEx = Some(miniport::shutdown_handler);
    driver_characteristics.CancelOidRequestHandler = Some(miniport::cancel_oid_request_handler);

    let mut ndis_handle = core::ptr::null_mut();

    let status = NdisMRegisterMiniportDriver(
        driver_object,
        registry_path,
        core::ptr::null_mut(), // No driver context
        &driver_characteristics,
        &mut ndis_handle,
    );

    if status == STATUS_SUCCESS {
        init_driver_globals(ndis_handle)?;
        Ok(())
    } else {
        Err(TunError::DriverCommunicationError(format!(
            "Failed to register miniport driver: 0x{:08X}",
            status.0
        )))
    }
}

/// Driver unload routine
#[cfg(driver_build)]
#[no_mangle]
pub extern "system" fn DriverUnload(driver_object: *mut DRIVER_OBJECT) {
    unsafe {
        if let Some(globals) = DRIVER_GLOBALS.as_ref() {
            NdisMDeregisterMiniportDriver(globals.ndis_handle);
        }
    }
}

/// Allocate memory in kernel space
pub fn allocate_memory(size: usize) -> Option<*mut u8> {
    unsafe {
        let ptr = ExAllocatePool2(
            POOL_FLAG_NON_PAGED,
            size,
            b"TunD\0".as_ptr() as u32, // Pool tag
        );

        if ptr.is_null() {
            None
        } else {
            Some(ptr as *mut u8)
        }
    }
}

/// Free memory in kernel space
pub unsafe fn free_memory(ptr: *mut u8) {
    if !ptr.is_null() {
        ExFreePoolWithTag(ptr as *mut _, b"TunD\0".as_ptr() as u32);
    }
}

/// Log a message to the kernel debugger
pub fn log_debug(message: &str) {
    unsafe {
        let message_cstr = alloc::ffi::CString::new(message).unwrap_or_default();
        DbgPrint(
            b"[TunDriver] %s\n\0".as_ptr() as *const i8,
            message_cstr.as_ptr(),
        );
    }
}

/// Log an error message
pub fn log_error(message: &str) {
    unsafe {
        let message_cstr = alloc::ffi::CString::new(message).unwrap_or_default();
        DbgPrint(
            b"[TunDriver ERROR] %s\n\0".as_ptr() as *const i8,
            message_cstr.as_ptr(),
        );
    }
}

/// Convert NTSTATUS to TunResult
pub fn ntstatus_to_result(status: NTSTATUS) -> TunResult<()> {
    if status == STATUS_SUCCESS {
        Ok(())
    } else {
        Err(crate::error::ntstatus_to_error(
            status.0,
            "kernel operation",
        ))
    }
}

// Remove duplicate allocator and panic handler - they're in main.rs
