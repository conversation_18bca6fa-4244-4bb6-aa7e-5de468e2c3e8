# Windows TUN Driver Build Script (PowerShell)
# This script builds both the userspace library and kernel driver

param(
    [switch]$SkipDriver,
    [switch]$SkipExamples,
    [switch]$Clean,
    [switch]$Install
)

Write-Host "Windows TUN Driver Build Script" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if (-not $isAdmin -and $Install) {
    Write-Warning "Not running as administrator. Driver installation will fail."
    Write-Host "Please run PowerShell as Administrator to install the driver." -ForegroundColor Yellow
}

# Set environment variables
$env:RUST_BACKTRACE = "1"
$env:CARGO_TARGET_DIR = "target"

# Clean if requested
if ($Clean) {
    Write-Host "Cleaning previous build..." -ForegroundColor Yellow
    cargo clean
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to clean build"
        exit 1
    }
}

# Build userspace library
Write-Host "Step 1: Building userspace library..." -ForegroundColor Cyan
cargo build --release --features userspace
if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to build userspace library"
    exit 1
}
Write-Host "✓ Userspace library built successfully" -ForegroundColor Green

# Build kernel driver (if not skipped)
if (-not $SkipDriver) {
    Write-Host "Step 2: Building kernel driver..." -ForegroundColor Cyan
    
    # Check if WDK is available
    $wdkPath = Get-ChildItem -Path "C:\Program Files (x86)\Windows Kits\10\Include" -Directory | Sort-Object Name -Descending | Select-Object -First 1
    if (-not $wdkPath) {
        Write-Warning "Windows Driver Kit (WDK) not found. Skipping driver build."
        Write-Host "To build the driver, install WDK from: https://docs.microsoft.com/en-us/windows-hardware/drivers/download-the-wdk" -ForegroundColor Yellow
    } else {
        Write-Host "Found WDK at: $($wdkPath.FullName)" -ForegroundColor Gray
        
        cargo build --release --features driver --target x86_64-pc-windows-msvc --bin tun-driver
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to build kernel driver"
            Write-Host "Make sure Windows Driver Kit (WDK) is properly installed and configured" -ForegroundColor Yellow
            exit 1
        }
        Write-Host "✓ Kernel driver built successfully" -ForegroundColor Green
        
        # Copy driver to .sys extension
        $driverSource = "target\x86_64-pc-windows-msvc\release\tun_driver.exe"
        $driverDest = "target\x86_64-pc-windows-msvc\release\tun_driver.sys"
        if (Test-Path $driverSource) {
            Copy-Item $driverSource $driverDest -Force
            Write-Host "✓ Driver copied to $driverDest" -ForegroundColor Green
        }
    }
} else {
    Write-Host "Step 2: Skipping kernel driver build" -ForegroundColor Yellow
}

# Build examples (if not skipped)
if (-not $SkipExamples) {
    Write-Host "Step 3: Building examples..." -ForegroundColor Cyan
    cargo build --release --examples
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to build examples"
        exit 1
    }
    Write-Host "✓ Examples built successfully" -ForegroundColor Green
} else {
    Write-Host "Step 3: Skipping examples build" -ForegroundColor Yellow
}

# Install driver (if requested and admin)
if ($Install -and $isAdmin) {
    Write-Host "Step 4: Installing driver..." -ForegroundColor Cyan
    
    $driverSys = "target\x86_64-pc-windows-msvc\release\tun_driver.sys"
    if (Test-Path $driverSys) {
        try {
            # Install driver using pnputil
            $result = pnputil /add-driver driver.inf /install
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Driver installed successfully" -ForegroundColor Green
            } else {
                Write-Error "Failed to install driver. Exit code: $LASTEXITCODE"
                Write-Host "Output: $result" -ForegroundColor Red
            }
        } catch {
            Write-Error "Exception during driver installation: $_"
        }
    } else {
        Write-Error "Driver file not found: $driverSys"
    }
} elseif ($Install -and -not $isAdmin) {
    Write-Warning "Cannot install driver without administrator privileges"
}

Write-Host ""
Write-Host "Build Summary:" -ForegroundColor Green
Write-Host "==============" -ForegroundColor Green

# List created files
$files = @()
if (Test-Path "target\release\windows_tun.dll") {
    $files += "target\release\windows_tun.dll (userspace library)"
}
if (Test-Path "target\x86_64-pc-windows-msvc\release\tun_driver.sys") {
    $files += "target\x86_64-pc-windows-msvc\release\tun_driver.sys (kernel driver)"
}
if (Test-Path "target\release\examples\simple_tun.exe") {
    $files += "target\release\examples\simple_tun.exe"
}
if (Test-Path "target\release\examples\packet_capture.exe") {
    $files += "target\release\examples\packet_capture.exe"
}

if ($files.Count -gt 0) {
    Write-Host "Files created:" -ForegroundColor Cyan
    foreach ($file in $files) {
        Write-Host "  ✓ $file" -ForegroundColor Gray
    }
} else {
    Write-Warning "No output files found"
}

Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
if (-not $Install) {
    Write-Host "1. To install the driver (run as Administrator):" -ForegroundColor White
    Write-Host "   pnputil /add-driver driver.inf /install" -ForegroundColor Gray
}
Write-Host "2. To test the library:" -ForegroundColor White
Write-Host "   target\release\examples\simple_tun.exe" -ForegroundColor Gray
Write-Host "3. To capture packets:" -ForegroundColor White
Write-Host "   target\release\examples\packet_capture.exe" -ForegroundColor Gray

Write-Host ""
Write-Host "Build completed!" -ForegroundColor Green
