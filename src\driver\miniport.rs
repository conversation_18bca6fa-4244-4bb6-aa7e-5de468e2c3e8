//! NDIS Miniport Driver Implementation
//!
//! This module implements the NDIS miniport driver callbacks for the TUN adapter.

use super::*;
use crate::error::{TunError, TunResult};
use wdk_sys::*;
use windows::Wdk::Foundation::*;
use windows::Wdk::NetworkManagement::Ndis::*;
use windows::Win32::Foundation::*;

/// Initialize handler for NDIS miniport
pub unsafe extern "system" fn initialize_handler(
    ndis_miniport_handle: NDIS_HANDLE,
    miniport_driver_context: NDIS_HANDLE,
    miniport_init_parameters: *mut NDIS_MINIPORT_INIT_PARAMETERS,
) -> NDIS_STATUS {
    log_debug("Miniport initialize handler called");

    match initialize_adapter(ndis_miniport_handle, miniport_init_parameters) {
        Ok(_) => NDIS_STATUS_SUCCESS,
        Err(e) => {
            log_error(&format!("Failed to initialize adapter: {:?}", e));
            NDIS_STATUS_FAILURE
        }
    }
}

/// Initialize a new adapter instance
unsafe fn initialize_adapter(
    ndis_miniport_handle: NDIS_HANDLE,
    init_parameters: *mut NDIS_MINIPORT_INIT_PARAMETERS,
) -> TunResult<()> {
    // Set miniport attributes
    let mut miniport_attributes = NDIS_MINIPORT_ADAPTER_ATTRIBUTES::default();
    miniport_attributes.Header.Type = NDIS_OBJECT_TYPE_MINIPORT_ADAPTER_ATTRIBUTES;
    miniport_attributes.Header.Size =
        core::mem::size_of::<NDIS_MINIPORT_ADAPTER_ATTRIBUTES>() as u16;
    miniport_attributes.Header.Revision = NDIS_MINIPORT_ADAPTER_ATTRIBUTES_REVISION_1;

    // Set general attributes
    let mut general_attributes = NDIS_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES::default();
    general_attributes.Header.Type = NDIS_OBJECT_TYPE_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES;
    general_attributes.Header.Size =
        core::mem::size_of::<NDIS_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES>() as u16;
    general_attributes.Header.Revision = NDIS_MINIPORT_ADAPTER_GENERAL_ATTRIBUTES_REVISION_2;

    general_attributes.MediaType = NdisMediumIP;
    general_attributes.PhysicalMediumType = NdisPhysicalMediumUnspecified;
    general_attributes.MtuSize = 1500;
    general_attributes.MaxXmitLinkSpeed = 1000000000; // 1 Gbps
    general_attributes.MaxRcvLinkSpeed = 1000000000; // 1 Gbps
    general_attributes.XmitLinkSpeed = 1000000000;
    general_attributes.RcvLinkSpeed = 1000000000;
    general_attributes.MediaConnectState = MediaConnectStateConnected;
    general_attributes.MediaDuplexState = MediaDuplexStateFull;
    general_attributes.LookaheadSize = 1500;
    general_attributes.PowerManagementCapabilities = core::ptr::null_mut();
    general_attributes.MacOptions = NDIS_MAC_OPTION_COPY_LOOKAHEAD_DATA
        | NDIS_MAC_OPTION_TRANSFERS_NOT_PEND
        | NDIS_MAC_OPTION_NO_LOOPBACK;

    general_attributes.SupportedPacketFilters = NDIS_PACKET_TYPE_DIRECTED
        | NDIS_PACKET_TYPE_MULTICAST
        | NDIS_PACKET_TYPE_ALL_MULTICAST
        | NDIS_PACKET_TYPE_BROADCAST;

    general_attributes.MaxMulticastListSize = 32;
    general_attributes.MacAddressLength = 6;

    // Generate a MAC address for the adapter
    let mac_address = generate_mac_address();
    general_attributes.PermanentMacAddress = mac_address.as_ptr() as *mut u8;
    general_attributes.CurrentMacAddress = mac_address.as_ptr() as *mut u8;

    general_attributes.RecvScaleCapabilities = core::ptr::null_mut();
    general_attributes.AccessType = NET_IF_ACCESS_BROADCAST;
    general_attributes.DirectionType = NET_IF_DIRECTION_SENDRECEIVE;
    general_attributes.ConnectionType = NET_IF_CONNECTION_DEDICATED;
    general_attributes.IfType = IF_TYPE_TUNNEL;
    general_attributes.IfConnectorPresent = FALSE;

    // Set supported statistics
    general_attributes.SupportedStatistics = NDIS_STATISTICS_FLAGS_VALID_DIRECTED_FRAMES_RCV
        | NDIS_STATISTICS_FLAGS_VALID_DIRECTED_FRAMES_XMIT
        | NDIS_STATISTICS_FLAGS_VALID_DIRECTED_BYTES_RCV
        | NDIS_STATISTICS_FLAGS_VALID_DIRECTED_BYTES_XMIT;

    let status = NdisMSetMiniportAttributes(
        ndis_miniport_handle,
        &general_attributes as *const _ as *mut NDIS_MINIPORT_ADAPTER_ATTRIBUTES,
    );

    if status != NDIS_STATUS_SUCCESS {
        return Err(TunError::AdapterCreationFailed(format!(
            "Failed to set miniport attributes: 0x{:08X}",
            status.0
        )));
    }

    // Create adapter instance
    let adapter = adapter::KernelAdapter::new(ndis_miniport_handle, mac_address)?;

    // Add to global adapter list
    let globals = get_driver_globals();
    globals.adapters.lock().push(adapter);

    log_debug("Adapter initialized successfully");
    Ok(())
}

/// Generate a random MAC address for the adapter
fn generate_mac_address() -> [u8; 6] {
    // Use a locally administered MAC address (bit 1 of first octet set)
    // Format: 02:xx:xx:xx:xx:xx where xx are random bytes
    let mut mac = [0u8; 6];
    mac[0] = 0x02; // Locally administered

    // Generate random bytes for the rest
    // In a real implementation, you'd use a proper random number generator
    // For now, use a simple pseudo-random approach
    unsafe {
        let tick_count = KeQueryTimeIncrement();
        for i in 1..6 {
            mac[i] = ((tick_count >> (i * 8)) & 0xFF) as u8;
        }
    }

    mac
}

/// Halt handler for NDIS miniport
pub unsafe extern "system" fn halt_handler(
    miniport_adapter_context: NDIS_HANDLE,
    halt_action: NDIS_HALT_ACTION,
) {
    log_debug("Miniport halt handler called");

    // Remove adapter from global list
    let globals = get_driver_globals();
    let mut adapters = globals.adapters.lock();
    adapters.retain(|adapter| adapter.ndis_handle != miniport_adapter_context);

    log_debug("Adapter halted successfully");
}

/// Unload handler for NDIS miniport
pub unsafe extern "system" fn unload_handler(driver_object: *mut DRIVER_OBJECT) {
    log_debug("Miniport unload handler called");
}

/// Pause handler for NDIS miniport
pub unsafe extern "system" fn pause_handler(
    miniport_adapter_context: NDIS_HANDLE,
    pause_parameters: *mut NDIS_MINIPORT_PAUSE_PARAMETERS,
) -> NDIS_STATUS {
    log_debug("Miniport pause handler called");
    NDIS_STATUS_SUCCESS
}

/// Restart handler for NDIS miniport
pub unsafe extern "system" fn restart_handler(
    miniport_adapter_context: NDIS_HANDLE,
    restart_parameters: *mut NDIS_MINIPORT_RESTART_PARAMETERS,
) -> NDIS_STATUS {
    log_debug("Miniport restart handler called");
    NDIS_STATUS_SUCCESS
}

/// OID request handler for NDIS miniport
pub unsafe extern "system" fn oid_request_handler(
    miniport_adapter_context: NDIS_HANDLE,
    oid_request: *mut NDIS_OID_REQUEST,
) -> NDIS_STATUS {
    if oid_request.is_null() {
        return NDIS_STATUS_INVALID_PARAMETER;
    }

    let request = &*oid_request;

    match request.RequestType {
        NdisRequestQueryInformation | NdisRequestQueryStatistics => {
            handle_query_oid(miniport_adapter_context, oid_request)
        }
        NdisRequestSetInformation => handle_set_oid(miniport_adapter_context, oid_request),
        NdisRequestMethod => NDIS_STATUS_NOT_SUPPORTED,
        _ => NDIS_STATUS_NOT_SUPPORTED,
    }
}

/// Handle query OID requests
unsafe fn handle_query_oid(
    miniport_adapter_context: NDIS_HANDLE,
    oid_request: *mut NDIS_OID_REQUEST,
) -> NDIS_STATUS {
    let request = &mut *oid_request;
    let query_info = &mut request.DATA.QUERY_INFORMATION;

    match query_info.Oid {
        OID_GEN_SUPPORTED_LIST => {
            // Return list of supported OIDs
            let supported_oids = get_supported_oids();
            let required_size = supported_oids.len() * core::mem::size_of::<NDIS_OID>();

            if query_info.InformationBufferLength < required_size as u32 {
                query_info.BytesNeeded = required_size as u32;
                return NDIS_STATUS_BUFFER_TOO_SHORT;
            }

            let buffer = core::slice::from_raw_parts_mut(
                query_info.InformationBuffer as *mut NDIS_OID,
                supported_oids.len(),
            );
            buffer.copy_from_slice(&supported_oids);

            query_info.BytesWritten = required_size as u32;
            NDIS_STATUS_SUCCESS
        }
        OID_GEN_HARDWARE_STATUS => set_query_result(query_info, &NdisHardwareStatusReady),
        OID_GEN_MEDIA_SUPPORTED | OID_GEN_MEDIA_IN_USE => {
            set_query_result(query_info, &NdisMediumIP)
        }
        OID_GEN_MAXIMUM_LOOKAHEAD => set_query_result(query_info, &1500u32),
        OID_GEN_MAXIMUM_FRAME_SIZE => set_query_result(query_info, &1500u32),
        OID_GEN_LINK_SPEED => {
            set_query_result(query_info, &1000000u32) // 1 Gbps in units of 100 bps
        }
        OID_GEN_TRANSMIT_BUFFER_SPACE => {
            set_query_result(query_info, &(64 * 1024u32)) // 64 KB
        }
        OID_GEN_RECEIVE_BUFFER_SPACE => {
            set_query_result(query_info, &(64 * 1024u32)) // 64 KB
        }
        OID_GEN_TRANSMIT_BLOCK_SIZE => set_query_result(query_info, &1500u32),
        OID_GEN_RECEIVE_BLOCK_SIZE => set_query_result(query_info, &1500u32),
        OID_GEN_VENDOR_ID => {
            set_query_result(query_info, &0x00FFFFFFu32) // Generic vendor ID
        }
        OID_GEN_VENDOR_DESCRIPTION => {
            let vendor_desc = b"Windows TUN Driver\0";
            set_query_buffer(query_info, vendor_desc)
        }
        OID_GEN_CURRENT_PACKET_FILTER => {
            set_query_result(query_info, &0u32) // No filter set initially
        }
        OID_GEN_MAXIMUM_TOTAL_SIZE => set_query_result(query_info, &1500u32),
        OID_GEN_MAC_OPTIONS => {
            let options = NDIS_MAC_OPTION_COPY_LOOKAHEAD_DATA
                | NDIS_MAC_OPTION_TRANSFERS_NOT_PEND
                | NDIS_MAC_OPTION_NO_LOOPBACK;
            set_query_result(query_info, &options)
        }
        OID_GEN_MEDIA_CONNECT_STATUS => set_query_result(query_info, &NdisMediaStateConnected),
        _ => {
            log_debug(&format!("Unsupported query OID: 0x{:08X}", query_info.Oid));
            NDIS_STATUS_NOT_SUPPORTED
        }
    }
}

/// Handle set OID requests
unsafe fn handle_set_oid(
    miniport_adapter_context: NDIS_HANDLE,
    oid_request: *mut NDIS_OID_REQUEST,
) -> NDIS_STATUS {
    let request = &mut *oid_request;
    let set_info = &mut request.DATA.SET_INFORMATION;

    match set_info.Oid {
        OID_GEN_CURRENT_PACKET_FILTER => {
            // Accept any packet filter setting
            set_info.BytesRead = core::mem::size_of::<u32>() as u32;
            NDIS_STATUS_SUCCESS
        }
        OID_GEN_CURRENT_LOOKAHEAD => {
            // Accept any lookahead setting
            set_info.BytesRead = core::mem::size_of::<u32>() as u32;
            NDIS_STATUS_SUCCESS
        }
        _ => {
            log_debug(&format!("Unsupported set OID: 0x{:08X}", set_info.Oid));
            NDIS_STATUS_NOT_SUPPORTED
        }
    }
}

/// Get list of supported OIDs
fn get_supported_oids() -> Vec<NDIS_OID> {
    vec![
        OID_GEN_SUPPORTED_LIST,
        OID_GEN_HARDWARE_STATUS,
        OID_GEN_MEDIA_SUPPORTED,
        OID_GEN_MEDIA_IN_USE,
        OID_GEN_MAXIMUM_LOOKAHEAD,
        OID_GEN_MAXIMUM_FRAME_SIZE,
        OID_GEN_LINK_SPEED,
        OID_GEN_TRANSMIT_BUFFER_SPACE,
        OID_GEN_RECEIVE_BUFFER_SPACE,
        OID_GEN_TRANSMIT_BLOCK_SIZE,
        OID_GEN_RECEIVE_BLOCK_SIZE,
        OID_GEN_VENDOR_ID,
        OID_GEN_VENDOR_DESCRIPTION,
        OID_GEN_CURRENT_PACKET_FILTER,
        OID_GEN_CURRENT_LOOKAHEAD,
        OID_GEN_MAXIMUM_TOTAL_SIZE,
        OID_GEN_MAC_OPTIONS,
        OID_GEN_MEDIA_CONNECT_STATUS,
    ]
}

/// Helper function to set query result
unsafe fn set_query_result<T>(
    query_info: &mut NDIS_REQUEST_QUERY_INFORMATION,
    value: &T,
) -> NDIS_STATUS {
    let size = core::mem::size_of::<T>();

    if query_info.InformationBufferLength < size as u32 {
        query_info.BytesNeeded = size as u32;
        return NDIS_STATUS_BUFFER_TOO_SHORT;
    }

    core::ptr::copy_nonoverlapping(
        value as *const T as *const u8,
        query_info.InformationBuffer as *mut u8,
        size,
    );

    query_info.BytesWritten = size as u32;
    NDIS_STATUS_SUCCESS
}

/// Helper function to set query buffer
unsafe fn set_query_buffer(
    query_info: &mut NDIS_REQUEST_QUERY_INFORMATION,
    buffer: &[u8],
) -> NDIS_STATUS {
    if query_info.InformationBufferLength < buffer.len() as u32 {
        query_info.BytesNeeded = buffer.len() as u32;
        return NDIS_STATUS_BUFFER_TOO_SHORT;
    }

    core::ptr::copy_nonoverlapping(
        buffer.as_ptr(),
        query_info.InformationBuffer as *mut u8,
        buffer.len(),
    );

    query_info.BytesWritten = buffer.len() as u32;
    NDIS_STATUS_SUCCESS
}

/// Send net buffer lists handler
pub unsafe extern "system" fn send_net_buffer_lists_handler(
    miniport_adapter_context: NDIS_HANDLE,
    net_buffer_lists: *mut NET_BUFFER_LIST,
    port_number: NDIS_PORT_NUMBER,
    send_flags: u32,
) {
    log_debug("Send net buffer lists handler called");

    // Process each net buffer list
    let mut current_nbl = net_buffer_lists;
    while !current_nbl.is_null() {
        let nbl = &mut *current_nbl;
        let next_nbl = nbl.Next;

        // Process net buffers in this list
        let mut current_nb = nbl.FirstNetBuffer;
        while !current_nb.is_null() {
            let nb = &*current_nb;

            // Extract packet data from net buffer
            if let Ok(packet_data) = extract_packet_from_net_buffer(nb) {
                // Forward packet to userspace
                forward_packet_to_userspace(miniport_adapter_context, &packet_data);
            }

            current_nb = nb.Next;
        }

        // Mark as completed
        nbl.Status = NDIS_STATUS_SUCCESS;
        current_nbl = next_nbl;
    }

    // Complete the send operation
    NdisMSendNetBufferListsComplete(miniport_adapter_context, net_buffer_lists, send_flags);
}

/// Return net buffer lists handler
pub unsafe extern "system" fn return_net_buffer_lists_handler(
    miniport_adapter_context: NDIS_HANDLE,
    net_buffer_lists: *mut NET_BUFFER_LIST,
    return_flags: u32,
) {
    log_debug("Return net buffer lists handler called");
    // Nothing to do for return - packets are already processed
}

/// Cancel send handler
pub unsafe extern "system" fn cancel_send_handler(
    miniport_adapter_context: NDIS_HANDLE,
    cancel_id: *const core::ffi::c_void,
) {
    log_debug("Cancel send handler called");
    // Nothing to cancel in our simple implementation
}

/// Check for hang handler
pub unsafe extern "system" fn check_for_hang_handler(
    miniport_adapter_context: NDIS_HANDLE,
) -> BOOLEAN {
    // Adapter never hangs in our implementation
    FALSE
}

/// Reset handler
pub unsafe extern "system" fn reset_handler(
    miniport_adapter_context: NDIS_HANDLE,
    addressing_reset: *mut BOOLEAN,
) -> NDIS_STATUS {
    log_debug("Reset handler called");
    *addressing_reset = FALSE;
    NDIS_STATUS_SUCCESS
}

/// Device PnP event notify handler
pub unsafe extern "system" fn device_pnp_event_notify_handler(
    miniport_adapter_context: NDIS_HANDLE,
    net_device_pnp_event: *mut NET_DEVICE_PNP_EVENT,
) {
    log_debug("Device PnP event notify handler called");
}

/// Shutdown handler
pub unsafe extern "system" fn shutdown_handler(
    miniport_adapter_context: NDIS_HANDLE,
    shutdown_action: NDIS_SHUTDOWN_ACTION,
) {
    log_debug("Shutdown handler called");
}

/// Cancel OID request handler
pub unsafe extern "system" fn cancel_oid_request_handler(
    miniport_adapter_context: NDIS_HANDLE,
    request_id: *const core::ffi::c_void,
) {
    log_debug("Cancel OID request handler called");
}

/// Extract packet data from a net buffer
unsafe fn extract_packet_from_net_buffer(net_buffer: *const NET_BUFFER) -> TunResult<Vec<u8>> {
    let nb = &*net_buffer;

    // Get the first MDL
    let mut current_mdl = nb.CurrentMdl;
    if current_mdl.is_null() {
        return Err(TunError::InvalidPacketFormat(
            "No MDL in net buffer".to_string(),
        ));
    }

    let mut packet_data = Vec::new();
    let mut remaining_length = nb.DataLength as usize;
    let mut current_offset = nb.CurrentMdlOffset as usize;

    // Traverse MDL chain and copy data
    while !current_mdl.is_null() && remaining_length > 0 {
        let mdl = &*current_mdl;

        // Get virtual address and length of this MDL
        let mdl_va = MmGetSystemAddressForMdlSafe(current_mdl, NormalPagePriority as i32);
        if mdl_va.is_null() {
            return Err(TunError::MemoryAllocationFailed);
        }

        let mdl_length = MmGetMdlByteCount(current_mdl) as usize;
        let available_length = mdl_length.saturating_sub(current_offset);
        let copy_length = remaining_length.min(available_length);

        if copy_length > 0 {
            let src_ptr = (mdl_va as *const u8).add(current_offset);
            let src_slice = core::slice::from_raw_parts(src_ptr, copy_length);
            packet_data.extend_from_slice(src_slice);

            remaining_length -= copy_length;
        }

        current_mdl = mdl.Next;
        current_offset = 0; // Only first MDL has offset
    }

    Ok(packet_data)
}

/// Forward packet to userspace
unsafe fn forward_packet_to_userspace(miniport_adapter_context: NDIS_HANDLE, packet_data: &[u8]) {
    // Find the adapter in our global list
    let globals = get_driver_globals();
    let adapters = globals.adapters.lock();

    for adapter in adapters.iter() {
        if adapter.ndis_handle == miniport_adapter_context {
            // Queue packet for userspace
            if let Err(e) = adapter.queue_received_packet(packet_data) {
                log_error(&format!("Failed to queue packet: {:?}", e));
            }
            break;
        }
    }
}
