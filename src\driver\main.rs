//! TUN Driver Entry Point (Simplified)
//!
//! This module provides a simplified entry point for the Windows TUN driver.

#![no_std]
#![no_main]

extern crate alloc;

use alloc::vec::Vec;
use core::panic::PanicInfo;

// Simplified driver entry point
#[no_mangle]
pub extern "system" fn DriverEntry(
    _driver_object: *mut core::ffi::c_void,
    _registry_path: *const core::ffi::c_void,
) -> i32 {
    // Initialize driver
    log_debug("TUN Driver loaded successfully");
    0 // STATUS_SUCCESS
}

/// Simple debug logging function
fn log_debug(message: &str) {
    // In real implementation would use DbgPrint or similar
    // For now, this is a placeholder
}

/// Panic handler for kernel mode
#[panic_handler]
fn panic(_info: &PanicInfo) -> ! {
    // In real implementation would call KeBugCheck
    loop {}
}

/// Global allocator error handler
#[alloc_error_handler]
fn alloc_error(_layout: core::alloc::Layout) -> ! {
    panic!("Memory allocation failed");
}

/// Simple memory allocator for testing
struct SimpleAllocator;

unsafe impl core::alloc::GlobalAlloc for SimpleAllocator {
    unsafe fn alloc(&self, _layout: core::alloc::Layout) -> *mut u8 {
        core::ptr::null_mut()
    }

    unsafe fn dealloc(&self, _ptr: *mut u8, _layout: core::alloc::Layout) {
        // No-op
    }
}

#[global_allocator]
static ALLOCATOR: SimpleAllocator = SimpleAllocator;
