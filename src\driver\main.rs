//! Windows TUN Driver Main Entry Point
//!
//! This is the main entry point for the kernel-mode TUN driver.
//! It sets up the driver and handles device creation and management.

#![no_std]
#![no_main]

extern crate alloc;

use alloc::vec::Vec;
use wdk_sys::*;
use windows::Wdk::Foundation::*;
use windows::Win32::Foundation::*;

// Import our driver modules
mod adapter;
mod ioctl;
mod miniport;
mod packet;

use crate::ioctl::handle_ioctl;

/// Driver device extension
#[repr(C)]
struct DeviceExtension {
    /// Device object pointer
    device_object: *mut DEVICE_OBJECT,
    /// Symbolic link name
    symbolic_link: UNICODE_STRING,
    /// Driver initialized flag
    initialized: bool,
}

/// Global driver object
static mut DRIVER_OBJECT_GLOBAL: *mut DRIVER_OBJECT = core::ptr::null_mut();

/// Driver entry point
#[no_mangle]
pub extern "system" fn DriverEntry(
    driver_object: *mut DRIVER_OBJECT,
    registry_path: *const UNICODE_STRING,
) -> NTSTATUS {
    unsafe {
        // Store global driver object
        DRIVER_OBJECT_GLOBAL = driver_object;

        // Initialize allocator
        init_allocator();

        // Initialize packet pool
        packet::init_packet_pool();

        // Set up driver callbacks
        let driver = &mut *driver_object;
        driver.MajorFunction[IRP_MJ_CREATE as usize] = Some(device_create);
        driver.MajorFunction[IRP_MJ_CLOSE as usize] = Some(device_close);
        driver.MajorFunction[IRP_MJ_DEVICE_CONTROL as usize] = Some(device_ioctl);
        driver.DriverUnload = Some(driver_unload);

        // Create device object
        match create_device_object(driver_object) {
            Ok(_) => {
                log_debug("TUN driver loaded successfully");

                // Register NDIS miniport driver
                match register_miniport_driver(driver_object, registry_path) {
                    Ok(_) => {
                        log_debug("NDIS miniport driver registered successfully");
                        STATUS_SUCCESS
                    }
                    Err(status) => {
                        log_error("Failed to register NDIS miniport driver");
                        cleanup_device_object(driver_object);
                        status
                    }
                }
            }
            Err(status) => {
                log_error("Failed to create device object");
                status
            }
        }
    }
}

/// Initialize the kernel allocator
unsafe fn init_allocator() {
    // Initialize the global allocator for no_std environment
    // This is a simplified implementation - in production you'd want a more robust allocator

    // For now, we rely on the allocator being initialized in the driver module
    log_debug("Allocator initialized");
}

/// Create the main device object
unsafe fn create_device_object(driver_object: *mut DRIVER_OBJECT) -> Result<(), NTSTATUS> {
    let device_name = create_unicode_string("\\Device\\TunDriver");
    let symbolic_link_name = create_unicode_string("\\DosDevices\\TunDriver");

    let mut device_object: *mut DEVICE_OBJECT = core::ptr::null_mut();

    // Create device object
    let status = IoCreateDevice(
        driver_object,
        core::mem::size_of::<DeviceExtension>() as u32,
        &device_name,
        FILE_DEVICE_NETWORK,
        FILE_DEVICE_SECURE_OPEN,
        FALSE,
        &mut device_object,
    );

    if status != STATUS_SUCCESS {
        return Err(status);
    }

    // Initialize device extension
    let device_extension = (*device_object).DeviceExtension as *mut DeviceExtension;
    (*device_extension).device_object = device_object;
    (*device_extension).symbolic_link = symbolic_link_name;
    (*device_extension).initialized = false;

    // Create symbolic link
    let status = IoCreateSymbolicLink(&symbolic_link_name, &device_name);
    if status != STATUS_SUCCESS {
        IoDeleteDevice(device_object);
        return Err(status);
    }

    // Set device flags
    (*device_object).Flags |= DO_BUFFERED_IO;
    (*device_object).Flags &= !DO_DEVICE_INITIALIZING;

    (*device_extension).initialized = true;

    log_debug("Device object created successfully");
    Ok(())
}

/// Cleanup device object
unsafe fn cleanup_device_object(driver_object: *mut DRIVER_OBJECT) {
    let driver = &*driver_object;
    if let Some(device_object) = driver.DeviceObject.as_ref() {
        let device_extension = device_object.DeviceExtension as *mut DeviceExtension;
        if !device_extension.is_null() && (*device_extension).initialized {
            // Delete symbolic link
            IoDeleteSymbolicLink(&(*device_extension).symbolic_link);
        }

        // Delete device object
        IoDeleteDevice(driver.DeviceObject);
    }
}

/// Register NDIS miniport driver
unsafe fn register_miniport_driver(
    driver_object: *mut DRIVER_OBJECT,
    registry_path: *const UNICODE_STRING,
) -> Result<(), NTSTATUS> {
    // For now, just return success - NDIS registration would be more complex
    log_debug("NDIS miniport driver registration placeholder");
    Ok(())
}

/// Device create handler
unsafe extern "system" fn device_create(
    device_object: *mut DEVICE_OBJECT,
    irp: *mut IRP,
) -> NTSTATUS {
    log_debug("Device create called");

    let irp_ref = &mut *irp;
    irp_ref.IoStatus.Status = STATUS_SUCCESS;
    irp_ref.IoStatus.Information = 0;

    IoCompleteRequest(irp, IO_NO_INCREMENT);
    STATUS_SUCCESS
}

/// Device close handler
unsafe extern "system" fn device_close(
    device_object: *mut DEVICE_OBJECT,
    irp: *mut IRP,
) -> NTSTATUS {
    log_debug("Device close called");

    let irp_ref = &mut *irp;
    irp_ref.IoStatus.Status = STATUS_SUCCESS;
    irp_ref.IoStatus.Information = 0;

    IoCompleteRequest(irp, IO_NO_INCREMENT);
    STATUS_SUCCESS
}

/// Device IOCTL handler
unsafe extern "system" fn device_ioctl(
    device_object: *mut DEVICE_OBJECT,
    irp: *mut IRP,
) -> NTSTATUS {
    handle_ioctl(device_object, irp)
}

/// Driver unload handler
unsafe extern "system" fn driver_unload(driver_object: *mut DRIVER_OBJECT) {
    log_debug("Driver unload called");

    // Cleanup device object
    cleanup_device_object(driver_object);

    // Deregister NDIS miniport driver (placeholder)
    log_debug("NDIS miniport driver deregistration placeholder");

    log_debug("TUN driver unloaded");
}

/// Create a UNICODE_STRING from a string literal
unsafe fn create_unicode_string(s: &str) -> UNICODE_STRING {
    let wide_string: Vec<u16> = s.encode_utf16().collect();
    let buffer_ptr = allocate_memory(wide_string.len() * 2)
        .expect("Failed to allocate memory for unicode string") as *mut u16;

    core::ptr::copy_nonoverlapping(wide_string.as_ptr(), buffer_ptr, wide_string.len());

    UNICODE_STRING {
        Length: (wide_string.len() * 2) as u16,
        MaximumLength: (wide_string.len() * 2) as u16,
        Buffer: buffer_ptr,
    }
}

/// Log a debug message
fn log_debug(message: &str) {
    unsafe {
        let message_cstr = alloc::ffi::CString::new(message).unwrap_or_default();
        DbgPrint(
            b"[TunDriver] %s\n\0".as_ptr() as *const i8,
            message_cstr.as_ptr(),
        );
    }
}

/// Log an error message
fn log_error(message: &str) {
    unsafe {
        let message_cstr = alloc::ffi::CString::new(message).unwrap_or_default();
        DbgPrint(
            b"[TunDriver ERROR] %s\n\0".as_ptr() as *const i8,
            message_cstr.as_ptr(),
        );
    }
}

/// Allocate memory in kernel space
fn allocate_memory(size: usize) -> Option<*mut u8> {
    unsafe {
        let ptr = ExAllocatePool2(POOL_FLAG_NON_PAGED, size, b"TunD\0".as_ptr() as u32);

        if ptr.is_null() {
            None
        } else {
            Some(ptr as *mut u8)
        }
    }
}

/// Free memory in kernel space
unsafe fn free_memory(ptr: *mut u8) {
    if !ptr.is_null() {
        ExFreePoolWithTag(ptr as *mut _, b"TunD\0".as_ptr() as u32);
    }
}

/// Panic handler for kernel mode
#[panic_handler]
fn panic(_info: &core::panic::PanicInfo) -> ! {
    unsafe {
        DbgPrint(b"[TunDriver PANIC] Driver panic occurred!\n\0".as_ptr() as *const i8);
        KeBugCheck(0xDEADBEEF);
    }
}

/// Global allocator for kernel mode
#[global_allocator]
static ALLOCATOR: linked_list_allocator::LockedHeap = linked_list_allocator::LockedHeap::empty();
