use thiserror::Error;

/// Errors that can occur during TUN adapter operations
#[derive(Error, Debug)]
pub enum TunError {
    #[error("Failed to create TUN adapter: {0}")]
    AdapterCreationFailed(String),

    #[error("Failed to configure adapter: {0}")]
    AdapterConfigurationFailed(String),

    #[error("Adapter not found: {0}")]
    AdapterNotFound(String),

    #[error("Invalid adapter state: {0}")]
    InvalidAdapterState(String),

    #[error("Packet operation failed: {0}")]
    PacketOperationFailed(String),

    #[error("Invalid packet format: {0}")]
    InvalidPacketFormat(String),

    #[error("Driver communication error: {0}")]
    DriverCommunicationError(String),

    #[error("IOCTL operation failed: {0}")]
    IoctlFailed(String),

    #[error("Memory allocation failed")]
    MemoryAllocationFailed,

    #[error("Invalid parameter: {0}")]
    InvalidParameter(String),

    #[error("Operation timeout")]
    Timeout,

    #[error("Permission denied: {0}")]
    PermissionDenied(String),

    #[error("Resource busy: {0}")]
    ResourceBusy(String),

    #[error("Windows API error: {0}")]
    WindowsApiError(#[from] windows::core::Error),

    #[error("I/O error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),

    #[error("UUID error: {0}")]
    UuidError(#[from] uuid::Error),

    #[error("Unknown error: {0}")]
    Unknown(String),
}

/// Result type for TUN operations
pub type TunResult<T> = Result<T, TunError>;

/// Convert Windows NTSTATUS to TunError
pub fn ntstatus_to_error(status: i32, context: &str) -> TunError {
    match status as u32 {
        0 => unreachable!("Success status should not be converted to error"),
        0xC0000005 => TunError::PermissionDenied(format!("Access violation in {}", context)),
        0xC000000D => TunError::InvalidParameter(format!("Invalid parameter in {}", context)),
        0xC0000017 => TunError::MemoryAllocationFailed,
        0xC00000BB => TunError::AdapterNotFound(format!("Device not found in {}", context)),
        0xC0000120 => TunError::Timeout,
        0xC000007F => TunError::InvalidParameter(format!("Procedure not found in {}", context)),
        _ => TunError::Unknown(format!("NTSTATUS 0x{:08X} in {}", status, context)),
    }
}

/// Convert Win32 error code to TunError
pub fn win32_to_error(error_code: u32, context: &str) -> TunError {
    match error_code {
        0 => unreachable!("Success code should not be converted to error"),
        5 => TunError::PermissionDenied(format!("Access denied in {}", context)),
        87 => TunError::InvalidParameter(format!("Invalid parameter in {}", context)),
        1168 => TunError::AdapterNotFound(format!("Element not found in {}", context)),
        1460 => TunError::Timeout,
        _ => TunError::Unknown(format!("Win32 error {} in {}", error_code, context)),
    }
}
