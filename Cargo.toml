[package]
name = "windows-tun-driver"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "Windows TUN driver implementation using Rust"
license = "MIT OR Apache-2.0"
repository = "https://github.com/yourusername/windows-tun-driver"

[lib]
name = "windows_tun"
crate-type = ["cdylib", "rlib"]

[[bin]]
name = "tun_driver"
path = "src/driver/main.rs"

[dependencies]
# Windows driver development (optional for driver builds)
wdk = { version = "0.2", optional = true }
wdk-sys = { version = "0.2", optional = true }
wdk-macros = { version = "0.2", optional = true }
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_System_IO",
    "Win32_System_Threading",
    "Win32_System_Diagnostics_Debug",
    "Win32_Storage_FileSystem",
    "Win32_Security",
    "Win32_Storage_FileSystem_FILE_ACCESS_RIGHTS",
] }

# Async runtime for userspace operations
tokio = { version = "1.0", features = ["full"], optional = true }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Error handling
thiserror = "1.0"
anyhow = "1.0"

# Logging
log = "0.4"
env_logger = "0.10"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
bitflags = "2.0"

[dev-dependencies]
tokio-test = "0.4"

[features]
default = ["userspace"]
userspace = ["tokio"]
driver = ["wdk", "wdk-sys", "wdk-macros"]

[profile.release]
panic = "abort"
lto = true
codegen-units = 1

[profile.dev]
panic = "abort"
