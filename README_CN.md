# Windows TUN 驱动程序

一个使用 Rust 和 [windows-drivers-rs](https://github.com/microsoft/windows-drivers-rs) 库实现的高性能 Windows TUN（三层）虚拟网络适配器。

## 功能特性

- **三层虚拟网络适配器**：创建工作在 IP 层的 TUN 适配器
- **IPv4 和 IPv6 支持**：完全支持 IPv4 和 IPv6 协议
- **高性能**：高效的数据包处理，开销最小
- **多适配器支持**：同时创建和管理多个 TUN 适配器
- **完整的 API**：易于使用的 Rust API，适用于用户空间应用程序
- **NDIS 微端口驱动**：基于 Windows NDIS 框架的内核模式驱动程序
- **异步支持**：可选的 async/await 支持，集成 Tokio

## 架构

项目包含两个主要组件：

1. **内核驱动程序** (`src/driver/`)：创建虚拟网络适配器的 NDIS 微端口驱动
2. **用户空间库** (`src/`)：用于创建和管理 TUN 适配器的 Rust 库

### 通信流程

```
用户空间应用程序
        ↕ (IOCTL)
    TUN 驱动程序
        ↕ (NDIS)
   Windows 网络栈
```

## 系统要求

- Windows 10/11 (x64)
- Rust 1.70+ 以及用于驱动编译的 `nightly` 工具链
- Windows 驱动程序开发工具包 (WDK)
- Visual Studio 构建工具
- 驱动安装需要管理员权限

## 安装

### 1. 安装依赖

```bash
# 安装 Rust nightly 工具链
rustup toolchain install nightly
rustup default nightly

# 添加必需的 Rust 目标
rustup target add x86_64-pc-windows-msvc
```

### 2. 构建项目

```bash
# 构建用户空间库
cargo build --release --features userspace

# 构建内核驱动（需要 WDK）
cargo build --release --features driver --target x86_64-pc-windows-msvc
```

### 3. 安装驱动程序

1. 将 `tun_driver.sys` 复制到安全位置
2. 使用设备管理器或 `pnputil` 安装：

```cmd
# 安装驱动程序（以管理员身份运行）
pnputil /add-driver driver.inf /install
```

## 使用方法

### 基本示例

```rust
use windows_tun::{AdapterConfig, AdapterManager, TunResult};
use std::net::Ipv4Addr;

#[tokio::main]
async fn main() -> TunResult<()> {
    // 初始化库
    windows_tun::init()?;

    // 创建适配器管理器
    let manager = AdapterManager::new();

    // 配置 TUN 适配器
    let config = AdapterConfig {
        name: "我的TUN".to_string(),
        ipv4_address: Some((Ipv4Addr::new(10, 0, 0, 1), 24)),
        mtu: 1500,
        ..Default::default()
    };

    // 创建适配器
    let adapter = manager.create_adapter(config)?;

    // 读取数据包
    while let Some(packet) = adapter.read_packet()? {
        println!("收到数据包：{} 字节", packet.length);

        // 处理数据包...

        // 回显数据包
        adapter.write_packet(&packet)?;
    }

    Ok(())
}
```

### 异步示例

```rust
use windows_tun::userspace::AsyncTunAdapter;

#[tokio::main]
async fn main() -> TunResult<()> {
    let adapter = create_adapter().await?;
    let mut async_adapter = AsyncTunAdapter::new(adapter);

    async_adapter.start().await?;

    while let Some(packet) = async_adapter.recv_packet().await {
        // 异步处理数据包
        process_packet(&packet).await;

        // 发送响应
        async_adapter.send_packet(response_packet).await?;
    }

    Ok(())
}
```

## 示例程序

`examples/` 目录包含几个使用示例：

- `simple_tun.rs`：基本 TUN 适配器，带数据包回显功能
- `packet_capture.rs`：数据包捕获和分析工具

运行示例：

```bash
cargo run --example simple_tun --features userspace
cargo run --example packet_capture --features userspace
```

## API 参考

### 核心类型

- `AdapterConfig`：TUN 适配器配置
- `TunAdapter`：TUN 适配器实例句柄
- `AdapterManager`：多适配器管理器
- `Packet`：网络数据包表示
- `PacketBuffer`：高效的数据包缓冲区

### 主要方法

- `AdapterManager::create_adapter()`：创建新的 TUN 适配器
- `TunAdapter::read_packet()`：从适配器读取数据包
- `TunAdapter::write_packet()`：向适配器写入数据包
- `TunAdapter::set_ipv4_address()`：配置 IPv4 地址
- `TunAdapter::stats()`：获取适配器统计信息

## 驱动架构

### NDIS 微端口驱动

内核驱动实现了具有以下特征的 NDIS 微端口驱动：

- **媒体类型**：`NdisMediumIP`（三层）
- **物理媒体**：`NdisPhysicalMediumUnspecified`
- **接口类型**：`IF_TYPE_TUNNEL`
- **连接类型**：`NET_IF_CONNECTION_DEDICATED`

### IOCTL 接口

用户空间和内核之间的通信使用 IOCTL 命令：

- `IOCTL_TUN_INITIALIZE`：初始化适配器
- `IOCTL_TUN_READ_PACKET`：从内核读取数据包
- `IOCTL_TUN_WRITE_PACKET`：向内核写入数据包
- `IOCTL_TUN_SET_IPV4`：配置 IPv4 地址
- `IOCTL_TUN_GET_STATS`：获取适配器统计信息

## 性能

驱动程序设计为高性能：

- 尽可能零拷贝数据包处理
- 使用数据包池的高效内存管理
- 最少的内核-用户空间转换
- 数据包队列使用无锁数据结构

## 安全性

安全考虑：

- 驱动安装需要管理员权限
- IOCTL 接口验证所有输入参数
- 内存分配使用标记池分配
- 驱动卸载时正确清理资源

## 故障排除

### 常见问题

1. **驱动安装失败**

   - 确保以管理员身份运行
   - 检查 Windows 版本兼容性
   - 验证驱动签名（如需要，禁用测试签名）

2. **适配器创建失败**

   - 检查驱动是否已加载：`sc query TunDriver`
   - 验证足够的权限
   - 检查 Windows 事件日志中的错误

3. **未收到数据包**
   - 验证适配器 IP 配置
   - 检查 Windows 路由表
   - 确保防火墙允许流量

### 调试模式

启用调试日志：

```rust
env_logger::init();
log::set_max_level(log::LevelFilter::Debug);
```

使用 DebugView 或 WinDbg 查看内核调试输出。

## 贡献

1. Fork 仓库
2. 创建功能分支
3. 进行更改
4. 为新功能添加测试
5. 提交 pull request

## 许可证

本项目采用以下任一许可证：

- Apache License, Version 2.0 ([LICENSE-APACHE](LICENSE-APACHE))
- MIT License ([LICENSE-MIT](LICENSE-MIT))

由您选择。

## 致谢

- [Microsoft windows-drivers-rs](https://github.com/microsoft/windows-drivers-rs) - Rust Windows 驱动开发框架
- [WireGuard WinTun](https://github.com/WireGuard/wintun) - TUN 驱动参考实现
- [OpenVPN TAP-Windows](https://github.com/OpenVPN/tap-windows6) - TAP 驱动参考

## 免责声明

本软件按"原样"提供，不提供任何保证。使用风险自负。作者不对本软件造成的任何损害负责。

## 支持

如有问题和支持：

- 在 GitHub 上提交 issue
- 查看文档
- 查看现有的 issue 和讨论

## 实现状态

### ✅ 已完成

- 完整的用户空间库和 API
- 数据包处理和验证
- 适配器管理和配置
- 异步 I/O 支持
- 示例程序和文档
- 单元测试（7/7 通过）

### 🚧 进行中

- 内核驱动程序（需要 WDK 环境配置）
- NDIS 微端口驱动实现
- 驱动程序安装和测试

### 📋 计划中

- 驱动程序签名和认证
- 性能优化
- 高级网络功能
- 监控和诊断工具

## 配置选项

### 适配器配置

```rust
let config = AdapterConfig {
    name: "我的TUN适配器".to_string(),        // 适配器显示名称
    description: "TUN虚拟网络适配器".to_string(), // 适配器描述
    ipv4_address: Some((Ipv4Addr::new(10, 0, 0, 1), 24)), // IPv4地址和子网掩码
    ipv6_address: None,                      // IPv6地址（可选）
    mtu: 1500,                              // 最大传输单元
    enable_ipv4: true,                      // 启用IPv4
    enable_ipv6: true,                      // 启用IPv6
    guid: None,                             // 自定义GUID（可选）
};
```

### 数据包处理

```rust
// 验证数据包
packet.validate()?;

// 获取数据包信息
let version = packet.ip_version()?;        // IP版本（4或6）
let src_ip = packet.source_ip()?;          // 源IP地址
let dst_ip = packet.destination_ip()?;     // 目标IP地址
let protocol = packet.protocol()?;         // 协议类型

// 创建数据包
let packet = Packet::new(data);            // 从Vec<u8>创建
let packet = Packet::from_slice(&bytes);   // 从切片创建
```

## 使用场景

### VPN 应用

```rust
// 创建VPN隧道适配器
let config = AdapterConfig {
    name: "VPN隧道".to_string(),
    ipv4_address: Some((Ipv4Addr::new(192, 168, 100, 1), 24)),
    mtu: 1400, // 考虑隧道开销
    ..Default::default()
};
```

### 网络监控

```rust
// 创建监控适配器
let config = AdapterConfig {
    name: "网络监控".to_string(),
    ipv4_address: Some((Ipv4Addr::new(172, 16, 0, 1), 16)),
    ..Default::default()
};

let adapter = manager.create_adapter(config)?;

// 监控所有流量
while let Some(packet) = adapter.read_packet()? {
    analyze_packet(&packet);
    log_traffic(&packet);
}
```

### 网络测试

```rust
// 创建测试适配器
let adapter = create_test_adapter().await?;

// 发送测试数据包
let test_packet = create_icmp_ping("*******")?;
adapter.write_packet(&test_packet)?;

// 等待响应
if let Some(response) = adapter.read_packet()? {
    println!("收到响应：{:?}", response);
}
```

## 构建脚本

项目提供了便捷的构建脚本：

### Windows 批处理脚本

```cmd
# 运行构建脚本
build.bat
```

### PowerShell 脚本

```powershell
# 基本构建
.\build.ps1

# 跳过驱动程序构建
.\build.ps1 -SkipDriver

# 清理并构建
.\build.ps1 -Clean

# 构建并安装驱动
.\build.ps1 -Install
```

## 开发指南

### 代码结构

```
src/
├── lib.rs              # 主库接口，导出公共API
├── error.rs            # 错误类型和处理
├── packet.rs           # 数据包结构和操作
├── adapter.rs          # 适配器管理和配置
├── main.rs             # 兼容性入口点
└── driver/             # 内核驱动程序
    ├── mod.rs          # 驱动主模块
    ├── miniport.rs     # NDIS微端口实现
    ├── adapter.rs      # 内核适配器管理
    ├── packet.rs       # 内核数据包处理
    ├── ioctl.rs        # IOCTL接口
    └── main.rs         # 驱动入口点
```

### 编码规范

- 使用 `cargo fmt` 格式化代码
- 使用 `cargo clippy` 检查代码质量
- 为公共 API 添加文档注释
- 编写单元测试和集成测试

### 测试

```bash
# 运行所有测试
cargo test --features userspace

# 运行特定测试
cargo test test_packet_validation --features userspace

# 运行文档测试
cargo test --doc --features userspace

# 生成测试覆盖率报告
cargo tarpaulin --features userspace
```

## 快速开始

查看 [QUICK_START.md](QUICK_START.md) 获取详细的入门指南。

查看 [IMPLEMENTATION_STATUS.md](IMPLEMENTATION_STATUS.md) 了解当前实现状态。
