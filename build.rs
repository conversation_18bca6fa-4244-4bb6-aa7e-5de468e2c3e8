use std::env;
use std::path::PathBuf;

fn main() {
    let target = env::var("TARGET").unwrap();
    let profile = env::var("PROFILE").unwrap();
    
    // Only build driver components when targeting Windows
    if target.contains("windows") {
        println!("cargo:rerun-if-changed=src/driver/");
        
        // Set up driver-specific build configuration
        if env::var("CARGO_FEATURE_DRIVER").is_ok() {
            println!("cargo:rustc-cfg=driver_build");
            
            // Driver-specific linker flags
            println!("cargo:rustc-link-arg=/DRIVER");
            println!("cargo:rustc-link-arg=/SUBSYSTEM:NATIVE");
            println!("cargo:rustc-link-arg=/ENTRY:DriverEntry");
            println!("cargo:rustc-link-arg=/NODEFAULTLIB");
            
            // Link against kernel libraries
            println!("cargo:rustc-link-lib=ntoskrnl");
            println!("cargo:rustc-link-lib=hal");
            println!("cargo:rustc-link-lib=ndis");
            
            // Set target directory for driver output
            let out_dir = env::var("OUT_DIR").unwrap();
            let driver_dir = PathBuf::from(&out_dir).join("driver");
            std::fs::create_dir_all(&driver_dir).unwrap();
            
            println!("cargo:rustc-env=DRIVER_OUTPUT_DIR={}", driver_dir.display());
        }
    }
    
    // Generate version information
    let version = env::var("CARGO_PKG_VERSION").unwrap();
    println!("cargo:rustc-env=DRIVER_VERSION={}", version);
    
    // Set up conditional compilation flags
    if profile == "debug" {
        println!("cargo:rustc-cfg=debug_build");
    }
}
