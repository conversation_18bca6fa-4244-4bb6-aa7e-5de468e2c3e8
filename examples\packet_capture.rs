//! Packet capture example
//! 
//! This example demonstrates how to capture and analyze packets from a TUN adapter.

use windows_tun::{AdapterConfig, AdapterManager, TunResult, Packet, IpProtocol};
use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr};
use std::time::{Duration, Instant};
use serde_json;

#[derive(Debug, Default)]
struct PacketStats {
    total_packets: u64,
    total_bytes: u64,
    ipv4_packets: u64,
    ipv6_packets: u64,
    tcp_packets: u64,
    udp_packets: u64,
    icmp_packets: u64,
    other_packets: u64,
    protocol_counts: HashMap<u8, u64>,
    source_ips: HashMap<IpAddr, u64>,
    destination_ips: HashMap<IpAddr, u64>,
}

#[tokio::main]
async fn main() -> TunResult<()> {
    // Initialize the library
    windows_tun::init()?;
    
    println!("TUN Packet Capture Example");
    println!("==========================");
    
    // Check if driver is available
    if !windows_tun::is_driver_available() {
        eprintln!("TUN driver is not available. Please install the driver first.");
        return Ok(());
    }
    
    // Create adapter manager
    let manager = AdapterManager::new();
    
    // Configure the TUN adapter for packet capture
    let config = AdapterConfig {
        name: "PacketCapture".to_string(),
        description: "Packet Capture TUN Adapter".to_string(),
        ipv4_address: Some((Ipv4Addr::new(192, 168, 100, 1), 24)),
        mtu: 1500,
        enable_ipv4: true,
        enable_ipv6: true,
        ..Default::default()
    };
    
    println!("Creating TUN adapter for packet capture...");
    let adapter = manager.create_adapter(config)?;
    println!("Adapter created with GUID: {}", adapter.guid());
    
    // Initialize packet statistics
    let mut stats = PacketStats::default();
    let mut last_stats_time = Instant::now();
    let stats_interval = Duration::from_secs(5);
    
    // Packet capture settings
    let capture_to_file = std::env::args().any(|arg| arg == "--save");
    let mut captured_packets = Vec::new();
    
    if capture_to_file {
        println!("Packet capture will be saved to 'captured_packets.json'");
    }
    
    println!("\nStarting packet capture...");
    println!("Press Ctrl+C to stop and display statistics");
    println!();
    
    // Main capture loop
    loop {
        // Check for packets
        match adapter.read_packet() {
            Ok(Some(packet)) => {
                // Analyze the packet
                analyze_packet(&packet, &mut stats);
                
                // Optionally save packet for later analysis
                if capture_to_file {
                    captured_packets.push(serialize_packet(&packet));
                }
                
                // Print packet summary
                print_packet_summary(&packet, stats.total_packets);
            }
            Ok(None) => {
                // No packet available
                tokio::time::sleep(Duration::from_millis(1)).await;
            }
            Err(e) => {
                eprintln!("Error reading packet: {}", e);
                tokio::time::sleep(Duration::from_millis(100)).await;
            }
        }
        
        // Print periodic statistics
        if last_stats_time.elapsed() >= stats_interval {
            print_statistics(&stats);
            last_stats_time = Instant::now();
        }
        
        // Check for shutdown signal
        if tokio::signal::ctrl_c().await.is_ok() {
            break;
        }
    }
    
    println!("\nCapture stopped. Final statistics:");
    print_final_statistics(&stats);
    
    // Save captured packets if requested
    if capture_to_file && !captured_packets.is_empty() {
        match save_captured_packets(&captured_packets) {
            Ok(_) => println!("Captured packets saved to 'captured_packets.json'"),
            Err(e) => eprintln!("Failed to save captured packets: {}", e),
        }
    }
    
    Ok(())
}

/// Analyze a packet and update statistics
fn analyze_packet(packet: &Packet, stats: &mut PacketStats) {
    stats.total_packets += 1;
    stats.total_bytes += packet.length as u64;
    
    // Analyze IP version
    if let Ok(version) = packet.ip_version() {
        match version {
            4 => stats.ipv4_packets += 1,
            6 => stats.ipv6_packets += 1,
            _ => {}
        }
    }
    
    // Analyze protocol
    if let Ok(protocol) = packet.protocol() {
        match protocol {
            IpProtocol::Tcp => stats.tcp_packets += 1,
            IpProtocol::Udp => stats.udp_packets += 1,
            IpProtocol::Icmp | IpProtocol::Icmpv6 => stats.icmp_packets += 1,
            IpProtocol::Other(proto) => {
                stats.other_packets += 1;
                *stats.protocol_counts.entry(proto).or_insert(0) += 1;
            }
        }
        
        // Count all protocols
        let proto_num: u8 = protocol.into();
        *stats.protocol_counts.entry(proto_num).or_insert(0) += 1;
    }
    
    // Analyze source and destination IPs
    if let Ok(src_ip) = packet.source_ip() {
        *stats.source_ips.entry(src_ip).or_insert(0) += 1;
    }
    
    if let Ok(dst_ip) = packet.destination_ip() {
        *stats.destination_ips.entry(dst_ip).or_insert(0) += 1;
    }
}

/// Print a summary of a single packet
fn print_packet_summary(packet: &Packet, packet_number: u64) {
    let mut summary = format!("Packet #{}: {} bytes", packet_number, packet.length);
    
    if let Ok(version) = packet.ip_version() {
        summary.push_str(&format!(" IPv{}", version));
    }
    
    if let Ok(protocol) = packet.protocol() {
        summary.push_str(&format!(" {:?}", protocol));
    }
    
    if let (Ok(src), Ok(dst)) = (packet.source_ip(), packet.destination_ip()) {
        summary.push_str(&format!(" {} -> {}", src, dst));
    }
    
    println!("{}", summary);
}

/// Print periodic statistics
fn print_statistics(stats: &PacketStats) {
    println!("\n--- Packet Statistics ---");
    println!("Total packets: {}", stats.total_packets);
    println!("Total bytes: {}", stats.total_bytes);
    println!("IPv4 packets: {}", stats.ipv4_packets);
    println!("IPv6 packets: {}", stats.ipv6_packets);
    println!("TCP packets: {}", stats.tcp_packets);
    println!("UDP packets: {}", stats.udp_packets);
    println!("ICMP packets: {}", stats.icmp_packets);
    println!("Other packets: {}", stats.other_packets);
    
    if stats.total_packets > 0 {
        let avg_size = stats.total_bytes as f64 / stats.total_packets as f64;
        println!("Average packet size: {:.2} bytes", avg_size);
    }
    
    println!("------------------------\n");
}

/// Print final detailed statistics
fn print_final_statistics(stats: &PacketStats) {
    println!("\n=== Final Packet Capture Statistics ===");
    println!("Total packets captured: {}", stats.total_packets);
    println!("Total bytes captured: {}", stats.total_bytes);
    
    if stats.total_packets > 0 {
        println!("\nProtocol Distribution:");
        println!("  IPv4: {} ({:.1}%)", stats.ipv4_packets, 
                 100.0 * stats.ipv4_packets as f64 / stats.total_packets as f64);
        println!("  IPv6: {} ({:.1}%)", stats.ipv6_packets,
                 100.0 * stats.ipv6_packets as f64 / stats.total_packets as f64);
        
        println!("\nTransport Protocols:");
        println!("  TCP: {} ({:.1}%)", stats.tcp_packets,
                 100.0 * stats.tcp_packets as f64 / stats.total_packets as f64);
        println!("  UDP: {} ({:.1}%)", stats.udp_packets,
                 100.0 * stats.udp_packets as f64 / stats.total_packets as f64);
        println!("  ICMP: {} ({:.1}%)", stats.icmp_packets,
                 100.0 * stats.icmp_packets as f64 / stats.total_packets as f64);
        
        println!("\nTop Source IPs:");
        let mut src_ips: Vec<_> = stats.source_ips.iter().collect();
        src_ips.sort_by(|a, b| b.1.cmp(a.1));
        for (ip, count) in src_ips.iter().take(5) {
            println!("  {}: {} packets", ip, count);
        }
        
        println!("\nTop Destination IPs:");
        let mut dst_ips: Vec<_> = stats.destination_ips.iter().collect();
        dst_ips.sort_by(|a, b| b.1.cmp(a.1));
        for (ip, count) in dst_ips.iter().take(5) {
            println!("  {}: {} packets", ip, count);
        }
        
        println!("\nProtocol Numbers:");
        let mut protocols: Vec<_> = stats.protocol_counts.iter().collect();
        protocols.sort_by(|a, b| b.1.cmp(a.1));
        for (proto, count) in protocols.iter().take(10) {
            println!("  Protocol {}: {} packets", proto, count);
        }
        
        let avg_size = stats.total_bytes as f64 / stats.total_packets as f64;
        println!("\nAverage packet size: {:.2} bytes", avg_size);
    }
    
    println!("=====================================");
}

/// Serialize a packet for saving
fn serialize_packet(packet: &Packet) -> serde_json::Value {
    let mut packet_info = serde_json::Map::new();
    
    packet_info.insert("length".to_string(), packet.length.into());
    packet_info.insert("timestamp".to_string(), packet.timestamp.into());
    
    if let Ok(version) = packet.ip_version() {
        packet_info.insert("ip_version".to_string(), version.into());
    }
    
    if let Ok(protocol) = packet.protocol() {
        let proto_num: u8 = protocol.into();
        packet_info.insert("protocol".to_string(), proto_num.into());
    }
    
    if let Ok(src_ip) = packet.source_ip() {
        packet_info.insert("source_ip".to_string(), src_ip.to_string().into());
    }
    
    if let Ok(dst_ip) = packet.destination_ip() {
        packet_info.insert("destination_ip".to_string(), dst_ip.to_string().into());
    }
    
    // Include first 64 bytes of packet data as hex
    let data_preview: String = packet.data.iter()
        .take(64)
        .map(|b| format!("{:02x}", b))
        .collect::<Vec<_>>()
        .join("");
    packet_info.insert("data_preview".to_string(), data_preview.into());
    
    serde_json::Value::Object(packet_info)
}

/// Save captured packets to a JSON file
fn save_captured_packets(packets: &[serde_json::Value]) -> Result<(), Box<dyn std::error::Error>> {
    let json_data = serde_json::to_string_pretty(packets)?;
    std::fs::write("captured_packets.json", json_data)?;
    Ok(())
}
