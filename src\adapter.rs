use crate::error::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>n<PERSON><PERSON><PERSON>};
use crate::packet::{Packet, PacketBuffer, MAX_PACKET_SIZE};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::{Ipv4Addr, Ipv6Addr};
use std::sync::{Arc, Mutex};
use uuid::Uuid;
use windows::Win32::Foundation::{HANDLE, INVALID_HANDLE_VALUE};

/// TUN adapter configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdapterConfig {
    /// Adapter name
    pub name: String,
    /// Adapter description
    pub description: String,
    /// IPv4 address and subnet mask
    pub ipv4_address: Option<(Ipv4Addr, u8)>,
    /// IPv6 address and prefix length
    pub ipv6_address: Option<(Ipv6Addr, u8)>,
    /// MTU size
    pub mtu: u16,
    /// Whether to enable IPv4
    pub enable_ipv4: bool,
    /// Whether to enable IPv6
    pub enable_ipv6: bool,
    /// Adapter GUID
    pub guid: Option<Uuid>,
}

impl Default for AdapterConfig {
    fn default() -> Self {
        Self {
            name: "TUN Adapter".to_string(),
            description: "Windows TUN Virtual Network Adapter".to_string(),
            ipv4_address: None,
            ipv6_address: None,
            mtu: 1500,
            enable_ipv4: true,
            enable_ipv6: true,
            guid: None,
        }
    }
}

/// TUN adapter state
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AdapterState {
    /// Adapter is being created
    Creating,
    /// Adapter is active and ready
    Active,
    /// Adapter is paused
    Paused,
    /// Adapter is being destroyed
    Destroying,
    /// Adapter has been destroyed
    Destroyed,
    /// Adapter is in error state
    Error,
}

/// Statistics for TUN adapter
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct AdapterStats {
    /// Packets received from network stack
    pub packets_received: u64,
    /// Packets sent to network stack
    pub packets_sent: u64,
    /// Bytes received from network stack
    pub bytes_received: u64,
    /// Bytes sent to network stack
    pub bytes_sent: u64,
    /// Packets dropped due to errors
    pub packets_dropped: u64,
    /// Packets dropped due to buffer full
    pub packets_dropped_buffer_full: u64,
    /// Number of errors encountered
    pub errors: u64,
}

/// TUN adapter handle for userspace operations
pub struct TunAdapter {
    /// Adapter configuration
    config: AdapterConfig,
    /// Current adapter state
    state: Arc<Mutex<AdapterState>>,
    /// Handle to the driver device
    device_handle: HANDLE,
    /// Adapter statistics
    stats: Arc<Mutex<AdapterStats>>,
    /// Packet receive buffer
    receive_buffer: Arc<Mutex<PacketBuffer>>,
    /// Packet send buffer
    send_buffer: Arc<Mutex<PacketBuffer>>,
    /// Adapter GUID
    guid: Uuid,
}

impl TunAdapter {
    /// Create a new TUN adapter with the given configuration
    pub fn create(config: AdapterConfig) -> TunResult<Self> {
        let guid = config.guid.unwrap_or_else(Uuid::new_v4);

        // Open handle to the TUN driver
        let device_handle = Self::open_driver_handle(&guid)?;

        let adapter = Self {
            config,
            state: Arc::new(Mutex::new(AdapterState::Creating)),
            device_handle,
            stats: Arc::new(Mutex::new(AdapterStats::default())),
            receive_buffer: Arc::new(Mutex::new(PacketBuffer::new(MAX_PACKET_SIZE))),
            send_buffer: Arc::new(Mutex::new(PacketBuffer::new(MAX_PACKET_SIZE))),
            guid,
        };

        // Initialize the adapter in the driver
        adapter.initialize_driver()?;

        // Set state to active
        *adapter.state.lock().unwrap() = AdapterState::Active;

        Ok(adapter)
    }

    /// Open a handle to the TUN driver device
    fn open_driver_handle(guid: &Uuid) -> TunResult<HANDLE> {
        use windows::core::PCWSTR;
        use windows::Win32::Storage::FileSystem::{
            CreateFileW, FILE_ATTRIBUTE_NORMAL, FILE_SHARE_READ, FILE_SHARE_WRITE, OPEN_EXISTING,
        };
        use windows::Win32::System::IO::{GENERIC_READ, GENERIC_WRITE};

        let device_path = format!("\\\\.\\TunAdapter_{}", guid.simple());
        let device_path_wide: Vec<u16> = device_path
            .encode_utf16()
            .chain(std::iter::once(0))
            .collect();

        let handle = unsafe {
            CreateFileW(
                PCWSTR(device_path_wide.as_ptr()),
                GENERIC_READ.0 | GENERIC_WRITE.0,
                FILE_SHARE_READ | FILE_SHARE_WRITE,
                None,
                OPEN_EXISTING,
                FILE_ATTRIBUTE_NORMAL,
                HANDLE::default(),
            )
        };

        match handle {
            Ok(h) if h != INVALID_HANDLE_VALUE => Ok(h),
            _ => Err(TunError::AdapterCreationFailed(
                "Failed to open driver handle".to_string(),
            )),
        }
    }

    /// Initialize the adapter in the driver
    fn initialize_driver(&self) -> TunResult<()> {
        // Send IOCTL to initialize adapter with configuration
        self.send_ioctl(0x1000, &self.config)?;
        Ok(())
    }

    /// Send an IOCTL command to the driver
    fn send_ioctl<T: Serialize>(&self, control_code: u32, data: &T) -> TunResult<Vec<u8>> {
        use windows::Win32::System::IO::DeviceIoControl;

        let input_data = serde_json::to_vec(data)?;
        let mut output_buffer = vec![0u8; 4096];
        let mut bytes_returned = 0u32;

        let result = unsafe {
            DeviceIoControl(
                self.device_handle,
                control_code,
                Some(input_data.as_ptr() as *const _),
                input_data.len() as u32,
                Some(output_buffer.as_mut_ptr() as *mut _),
                output_buffer.len() as u32,
                Some(&mut bytes_returned),
                None,
            )
        };

        if result.is_ok() {
            output_buffer.truncate(bytes_returned as usize);
            Ok(output_buffer)
        } else {
            Err(TunError::IoctlFailed(format!(
                "IOCTL 0x{:08X} failed",
                control_code
            )))
        }
    }

    /// Get adapter configuration
    pub fn config(&self) -> &AdapterConfig {
        &self.config
    }

    /// Get adapter GUID
    pub fn guid(&self) -> Uuid {
        self.guid
    }

    /// Get current adapter state
    pub fn state(&self) -> AdapterState {
        *self.state.lock().unwrap()
    }

    /// Get adapter statistics
    pub fn stats(&self) -> AdapterStats {
        self.stats.lock().unwrap().clone()
    }

    /// Set IPv4 address
    pub fn set_ipv4_address(&mut self, address: Ipv4Addr, prefix_length: u8) -> TunResult<()> {
        self.config.ipv4_address = Some((address, prefix_length));
        self.send_ioctl(0x1001, &self.config)?;
        Ok(())
    }

    /// Set IPv6 address
    pub fn set_ipv6_address(&mut self, address: Ipv6Addr, prefix_length: u8) -> TunResult<()> {
        self.config.ipv6_address = Some((address, prefix_length));
        self.send_ioctl(0x1002, &self.config)?;
        Ok(())
    }

    /// Set MTU
    pub fn set_mtu(&mut self, mtu: u16) -> TunResult<()> {
        self.config.mtu = mtu;
        self.send_ioctl(0x1003, &mtu)?;
        Ok(())
    }

    /// Read a packet from the adapter
    pub fn read_packet(&self) -> TunResult<Option<Packet>> {
        let mut buffer = self.receive_buffer.lock().unwrap();
        buffer.clear();

        // Try to read packet from driver
        match self.send_ioctl(0x2000, &()) {
            Ok(data) if !data.is_empty() => {
                let packet = Packet::from_slice(&data);
                packet.validate()?;

                // Update statistics
                let mut stats = self.stats.lock().unwrap();
                stats.packets_received += 1;
                stats.bytes_received += packet.length as u64;

                Ok(Some(packet))
            }
            Ok(_) => Ok(None), // No packet available
            Err(e) => Err(e),
        }
    }

    /// Write a packet to the adapter
    pub fn write_packet(&self, packet: &Packet) -> TunResult<()> {
        // Validate packet before sending
        packet.validate()?;

        // Send packet to driver
        self.send_ioctl(0x2001, &packet.data)?;

        // Update statistics
        let mut stats = self.stats.lock().unwrap();
        stats.packets_sent += 1;
        stats.bytes_sent += packet.length as u64;

        Ok(())
    }

    /// Pause the adapter
    pub fn pause(&self) -> TunResult<()> {
        self.send_ioctl(0x3000, &())?;
        *self.state.lock().unwrap() = AdapterState::Paused;
        Ok(())
    }

    /// Resume the adapter
    pub fn resume(&self) -> TunResult<()> {
        self.send_ioctl(0x3001, &())?;
        *self.state.lock().unwrap() = AdapterState::Active;
        Ok(())
    }
}

impl Drop for TunAdapter {
    fn drop(&mut self) {
        use windows::Win32::Foundation::CloseHandle;

        // Set state to destroying
        *self.state.lock().unwrap() = AdapterState::Destroying;

        // Send destroy command to driver
        let _ = self.send_ioctl(0x4000, &());

        // Close device handle
        if self.device_handle != INVALID_HANDLE_VALUE {
            unsafe {
                let _ = CloseHandle(self.device_handle);
            }
        }

        // Set final state
        *self.state.lock().unwrap() = AdapterState::Destroyed;
    }
}

/// TUN adapter manager for handling multiple adapters
pub struct AdapterManager {
    adapters: Arc<Mutex<HashMap<Uuid, Arc<TunAdapter>>>>,
}

impl AdapterManager {
    /// Create a new adapter manager
    pub fn new() -> Self {
        Self {
            adapters: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// Create a new adapter
    pub fn create_adapter(&self, config: AdapterConfig) -> TunResult<Arc<TunAdapter>> {
        let adapter = Arc::new(TunAdapter::create(config)?);
        let guid = adapter.guid();

        self.adapters.lock().unwrap().insert(guid, adapter.clone());

        Ok(adapter)
    }

    /// Get an adapter by GUID
    pub fn get_adapter(&self, guid: &Uuid) -> Option<Arc<TunAdapter>> {
        self.adapters.lock().unwrap().get(guid).cloned()
    }

    /// Remove an adapter
    pub fn remove_adapter(&self, guid: &Uuid) -> TunResult<()> {
        self.adapters.lock().unwrap().remove(guid);
        Ok(())
    }

    /// List all adapters
    pub fn list_adapters(&self) -> Vec<Uuid> {
        self.adapters.lock().unwrap().keys().cloned().collect()
    }
}

impl Default for AdapterManager {
    fn default() -> Self {
        Self::new()
    }
}
