use crate::error::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr};

/// Maximum packet size for TUN interface (MTU + headers)
pub const MAX_PACKET_SIZE: usize = 1500 + 40; // MTU + IPv6 header

/// IP protocol numbers
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
#[repr(u8)]
pub enum IpProtocol {
    Icmp = 1,
    Tcp = 6,
    Udp = 17,
    Icmpv6 = 58,
    Other(u8),
}

impl From<u8> for IpProtocol {
    fn from(value: u8) -> Self {
        match value {
            1 => IpProtocol::Icmp,
            6 => IpProtocol::Tcp,
            17 => IpProtocol::Udp,
            58 => IpProtocol::Icmpv6,
            other => IpProtocol::Other(other),
        }
    }
}

impl From<IpProtocol> for u8 {
    fn from(protocol: IpProtocol) -> Self {
        match protocol {
            IpProtocol::Icmp => 1,
            IpProtocol::Tcp => 6,
            IpProtocol::Udp => 17,
            IpProtocol::Icmpv6 => 58,
            IpProtocol::Other(value) => value,
        }
    }
}

/// Represents a network packet
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Packet {
    /// Raw packet data
    pub data: Vec<u8>,
    /// Packet length
    pub length: usize,
    /// Timestamp when packet was captured/created
    pub timestamp: u64,
}

impl Packet {
    /// Create a new packet from raw data
    pub fn new(data: Vec<u8>) -> Self {
        let length = data.len();
        Self {
            data,
            length,
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
        }
    }

    /// Create a packet from a slice
    pub fn from_slice(data: &[u8]) -> Self {
        Self::new(data.to_vec())
    }

    /// Get the IP version of the packet
    pub fn ip_version(&self) -> TunResult<u8> {
        if self.data.is_empty() {
            return Err(TunError::InvalidPacketFormat("Empty packet".to_string()));
        }

        let version = (self.data[0] >> 4) & 0x0F;
        if version != 4 && version != 6 {
            return Err(TunError::InvalidPacketFormat(format!(
                "Invalid IP version: {}",
                version
            )));
        }

        Ok(version)
    }

    /// Get source IP address
    pub fn source_ip(&self) -> TunResult<IpAddr> {
        match self.ip_version()? {
            4 => {
                if self.data.len() < 20 {
                    return Err(TunError::InvalidPacketFormat(
                        "IPv4 packet too short".to_string(),
                    ));
                }
                let ip_bytes = [self.data[12], self.data[13], self.data[14], self.data[15]];
                Ok(IpAddr::V4(Ipv4Addr::from(ip_bytes)))
            }
            6 => {
                if self.data.len() < 40 {
                    return Err(TunError::InvalidPacketFormat(
                        "IPv6 packet too short".to_string(),
                    ));
                }
                let mut ip_bytes = [0u8; 16];
                ip_bytes.copy_from_slice(&self.data[8..24]);
                Ok(IpAddr::V6(Ipv6Addr::from(ip_bytes)))
            }
            _ => Err(TunError::InvalidPacketFormat(
                "Unknown IP version".to_string(),
            )),
        }
    }

    /// Get destination IP address
    pub fn destination_ip(&self) -> TunResult<IpAddr> {
        match self.ip_version()? {
            4 => {
                if self.data.len() < 20 {
                    return Err(TunError::InvalidPacketFormat(
                        "IPv4 packet too short".to_string(),
                    ));
                }
                let ip_bytes = [self.data[16], self.data[17], self.data[18], self.data[19]];
                Ok(IpAddr::V4(Ipv4Addr::from(ip_bytes)))
            }
            6 => {
                if self.data.len() < 40 {
                    return Err(TunError::InvalidPacketFormat(
                        "IPv6 packet too short".to_string(),
                    ));
                }
                let mut ip_bytes = [0u8; 16];
                ip_bytes.copy_from_slice(&self.data[24..40]);
                Ok(IpAddr::V6(Ipv6Addr::from(ip_bytes)))
            }
            _ => Err(TunError::InvalidPacketFormat(
                "Unknown IP version".to_string(),
            )),
        }
    }

    /// Get protocol
    pub fn protocol(&self) -> TunResult<IpProtocol> {
        match self.ip_version()? {
            4 => {
                if self.data.len() < 20 {
                    return Err(TunError::InvalidPacketFormat(
                        "IPv4 packet too short".to_string(),
                    ));
                }
                Ok(IpProtocol::from(self.data[9]))
            }
            6 => {
                if self.data.len() < 40 {
                    return Err(TunError::InvalidPacketFormat(
                        "IPv6 packet too short".to_string(),
                    ));
                }
                Ok(IpProtocol::from(self.data[6]))
            }
            _ => Err(TunError::InvalidPacketFormat(
                "Unknown IP version".to_string(),
            )),
        }
    }

    /// Validate packet integrity
    pub fn validate(&self) -> TunResult<()> {
        if self.data.is_empty() {
            return Err(TunError::InvalidPacketFormat("Empty packet".to_string()));
        }

        if self.data.len() > MAX_PACKET_SIZE {
            return Err(TunError::InvalidPacketFormat(
                "Packet too large".to_string(),
            ));
        }

        if self.length != self.data.len() {
            return Err(TunError::InvalidPacketFormat("Length mismatch".to_string()));
        }

        // Validate IP header
        let version = self.ip_version()?;
        match version {
            4 => self.validate_ipv4(),
            6 => self.validate_ipv6(),
            _ => Err(TunError::InvalidPacketFormat(
                "Invalid IP version".to_string(),
            )),
        }
    }

    fn validate_ipv4(&self) -> TunResult<()> {
        if self.data.len() < 20 {
            return Err(TunError::InvalidPacketFormat(
                "IPv4 header too short".to_string(),
            ));
        }

        let header_length = ((self.data[0] & 0x0F) * 4) as usize;
        if header_length < 20 || header_length > self.data.len() {
            return Err(TunError::InvalidPacketFormat(
                "Invalid IPv4 header length".to_string(),
            ));
        }

        let total_length = u16::from_be_bytes([self.data[2], self.data[3]]) as usize;
        if total_length > self.data.len() {
            return Err(TunError::InvalidPacketFormat(
                "IPv4 total length exceeds packet size".to_string(),
            ));
        }

        Ok(())
    }

    fn validate_ipv6(&self) -> TunResult<()> {
        if self.data.len() < 40 {
            return Err(TunError::InvalidPacketFormat(
                "IPv6 header too short".to_string(),
            ));
        }

        let payload_length = u16::from_be_bytes([self.data[4], self.data[5]]) as usize;
        if payload_length + 40 > self.data.len() {
            return Err(TunError::InvalidPacketFormat(
                "IPv6 payload length exceeds packet size".to_string(),
            ));
        }

        Ok(())
    }
}

/// Packet buffer for efficient packet handling
pub struct PacketBuffer {
    buffer: Vec<u8>,
    capacity: usize,
}

impl PacketBuffer {
    /// Create a new packet buffer with specified capacity
    pub fn new(capacity: usize) -> Self {
        Self {
            buffer: Vec::with_capacity(capacity),
            capacity,
        }
    }

    /// Create a packet buffer with default capacity
    pub fn default() -> Self {
        Self::new(MAX_PACKET_SIZE)
    }

    /// Clear the buffer
    pub fn clear(&mut self) {
        self.buffer.clear();
    }

    /// Get buffer as slice
    pub fn as_slice(&self) -> &[u8] {
        &self.buffer
    }

    /// Get mutable buffer
    pub fn as_mut_slice(&mut self) -> &mut [u8] {
        &mut self.buffer
    }

    /// Set buffer length
    pub fn set_len(&mut self, len: usize) {
        if len <= self.capacity {
            unsafe {
                self.buffer.set_len(len);
            }
        }
    }

    /// Get buffer length
    pub fn len(&self) -> usize {
        self.buffer.len()
    }

    /// Check if buffer is empty
    pub fn is_empty(&self) -> bool {
        self.buffer.is_empty()
    }

    /// Convert to packet
    pub fn to_packet(&self) -> Packet {
        Packet::from_slice(&self.buffer)
    }
}
