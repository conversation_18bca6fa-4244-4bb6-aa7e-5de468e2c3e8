# Windows TUN Driver - 快速开始指南

## 环境要求

- Windows 10/11 (x64)
- Rust 1.70+ 
- Visual Studio Build Tools
- 管理员权限（用于驱动安装）

## 快速安装

### 1. 克隆项目
```bash
git clone <repository-url>
cd windows-tun-driver
```

### 2. 安装 Rust 依赖
```bash
# 安装 Rust（如果尚未安装）
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 安装必要的工具链
rustup toolchain install stable
rustup target add x86_64-pc-windows-msvc
```

### 3. 编译项目
```bash
# 编译用户空间库
cargo build --release --features userspace

# 编译示例程序
cargo build --release --examples --features userspace
```

### 4. 运行测试
```bash
# 运行所有测试
cargo test --features userspace

# 运行特定测试
cargo test test_packet_validation --features userspace
```

## 使用示例

### 基本 TUN 适配器使用

```rust
use windows_tun::{Adapter<PERSON>onfig, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TunResult};
use std::net::Ipv4Addr;

#[tokio::main]
async fn main() -> TunResult<()> {
    // 初始化库
    windows_tun::init()?;
    
    // 创建适配器管理器
    let manager = AdapterManager::new();
    
    // 配置 TUN 适配器
    let config = AdapterConfig {
        name: "MyTUN".to_string(),
        description: "我的 TUN 适配器".to_string(),
        ipv4_address: Some((Ipv4Addr::new(10, 0, 0, 1), 24)),
        mtu: 1500,
        enable_ipv4: true,
        enable_ipv6: false,
        ..Default::default()
    };
    
    // 创建适配器
    let adapter = manager.create_adapter(config)?;
    println!("TUN 适配器已创建，GUID: {}", adapter.guid());
    
    // 数据包处理循环
    loop {
        // 读取数据包
        if let Some(packet) = adapter.read_packet()? {
            println!("收到数据包: {} 字节", packet.length);
            
            // 处理数据包...
            
            // 回显数据包
            adapter.write_packet(&packet)?;
        }
        
        // 避免 CPU 占用过高
        tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
    }
}
```

### 异步数据包处理

```rust
use windows_tun::userspace::AsyncTunAdapter;

#[tokio::main]
async fn main() -> TunResult<()> {
    let adapter = create_tun_adapter().await?;
    let mut async_adapter = AsyncTunAdapter::new(adapter);
    
    // 启动异步处理
    async_adapter.start().await?;
    
    // 异步接收数据包
    while let Some(packet) = async_adapter.recv_packet().await {
        println!("异步收到数据包: {} 字节", packet.length);
        
        // 异步发送响应
        let response = create_response_packet(&packet);
        async_adapter.send_packet(response).await?;
    }
    
    Ok(())
}
```

## 运行示例程序

### 1. 简单 TUN 示例
```bash
# 运行基本 TUN 适配器示例
target/release/examples/simple_tun.exe
```

这个示例会：
- 创建一个 TUN 适配器
- 监听网络数据包
- 实现简单的 ICMP 回显功能
- 显示统计信息

### 2. 数据包捕获示例
```bash
# 运行数据包捕获工具
target/release/examples/packet_capture.exe

# 保存捕获的数据包到文件
target/release/examples/packet_capture.exe --save
```

这个示例会：
- 捕获所有通过 TUN 适配器的数据包
- 分析数据包内容（IP 版本、协议、地址等）
- 显示详细的统计信息
- 可选择保存数据包到 JSON 文件

## 配置选项

### AdapterConfig 参数说明

```rust
let config = AdapterConfig {
    name: "适配器名称".to_string(),           // 适配器显示名称
    description: "适配器描述".to_string(),     // 适配器描述
    ipv4_address: Some((Ipv4Addr::new(10, 0, 0, 1), 24)), // IPv4 地址和子网掩码
    ipv6_address: None,                       // IPv6 地址（可选）
    mtu: 1500,                               // 最大传输单元
    enable_ipv4: true,                       // 启用 IPv4
    enable_ipv6: false,                      // 启用 IPv6
    guid: None,                              // 自定义 GUID（可选）
};
```

### 数据包处理选项

```rust
// 验证数据包
packet.validate()?;

// 获取数据包信息
let version = packet.ip_version()?;
let src_ip = packet.source_ip()?;
let dst_ip = packet.destination_ip()?;
let protocol = packet.protocol()?;

// 创建新数据包
let new_packet = Packet::new(data);
let from_slice = Packet::from_slice(&bytes);
```

## 故障排除

### 常见问题

1. **编译错误**
   ```bash
   # 确保使用正确的功能标志
   cargo build --features userspace
   
   # 清理并重新编译
   cargo clean
   cargo build --features userspace
   ```

2. **权限错误**
   - 确保以管理员身份运行
   - 检查 Windows 防火墙设置
   - 验证用户账户控制 (UAC) 设置

3. **驱动程序未找到**
   - 当前版本的内核驱动需要 WDK 环境
   - 用户空间库可以独立使用进行开发和测试

### 调试技巧

1. **启用详细日志**
   ```rust
   env_logger::init();
   log::set_max_level(log::LevelFilter::Debug);
   ```

2. **检查适配器状态**
   ```rust
   println!("适配器状态: {:?}", adapter.state());
   println!("统计信息: {:?}", adapter.stats());
   ```

3. **数据包调试**
   ```rust
   println!("数据包详情: {:?}", packet);
   println!("原始数据: {:02x?}", &packet.data[..20]); // 显示前20字节
   ```

## 下一步

1. **学习更多示例**: 查看 `examples/` 目录中的其他示例
2. **阅读 API 文档**: 运行 `cargo doc --open` 查看完整 API 文档
3. **参与开发**: 查看 `IMPLEMENTATION_STATUS.md` 了解项目状态
4. **报告问题**: 在 GitHub 上提交 issue 或 pull request

## 获取帮助

- 查看 `README.md` 获取详细文档
- 阅读 `IMPLEMENTATION_STATUS.md` 了解当前状态
- 查看源代码中的注释和文档
- 在 GitHub 上提交问题或建议
