;
; Windows TUN Driver Installation File
; Copyright (c) 2024. All rights reserved.
;

[Version]
Signature="$WINDOWS NT$"
Class=Net
ClassGUID={4d36e972-e325-11ce-bfc1-08002be10318}
Provider=%ManufacturerName%
CatalogFile=tun_driver.cat
DriverVer=01/01/2024,1.0.0.0
PnpLockdown=1

[DestinationDirs]
DefaultDestDir = 12
TunDriver_Device_CoInstaller_CopyFiles = 11

[SourceDisksNames]
1 = %DiskName%,,,""

[SourceDisksFiles]
tun_driver.sys = 1,,
WdfCoInstaller$KMDFCOINSTALLERVERSION$.dll = 1

[Manufacturer]
%ManufacturerName%=Standard,NT$ARCH$

[Standard.NT$ARCH$]
%TunDriver.DeviceDesc%=TunDriver_Device, Root\TunDriver

[TunDriver_Device.NT]
CopyFiles=Drivers_Dir

[Drivers_Dir]
tun_driver.sys

[TunDriver_Device.NT.HW]
AddReg=TunDriver_Device.NT.AddReg

[TunDriver_Device.NT.AddReg]
HKR,,DeviceCharacteristics,0x10001,0x0100         ; Use same security checks on relative opens
HKR,,Security,,"D:P(A;;GA;;;BA)(A;;GA;;;SY)"      ; Allow generic-all access to Built-in administrators and Local system

[TunDriver_Device.NT.Services]
AddService = TunDriver,%SPSVCINST_ASSOCSERVICE%, TunDriver_Service_Inst

[TunDriver_Service_Inst]
DisplayName    = %TunDriver.SVCDESC%
ServiceType    = 1               ; SERVICE_KERNEL_DRIVER
StartType      = 3               ; SERVICE_DEMAND_START
ErrorControl   = 1               ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\tun_driver.sys

[TunDriver_Device.NT.CoInstallers]
AddReg=TunDriver_Device_CoInstaller_AddReg
CopyFiles=TunDriver_Device_CoInstaller_CopyFiles

[TunDriver_Device_CoInstaller_AddReg]
HKR,,CoInstallers32,0x00010000, "WdfCoInstaller$KMDFCOINSTALLERVERSION$.dll,WdfCoInstaller"

[TunDriver_Device_CoInstaller_CopyFiles]
WdfCoInstaller$KMDFCOINSTALLERVERSION$.dll

[TunDriver_Device.NT.Wdf]
KmdfService =  TunDriver, TunDriver_wdfsect

[TunDriver_wdfsect]
KmdfLibraryVersion = $KMDFVERSION$

[Strings]
SPSVCINST_ASSOCSERVICE= 0x00000002
ManufacturerName="Windows TUN Driver"
DiskName = "Windows TUN Driver Installation Disk"
TunDriver.DeviceDesc = "Windows TUN Virtual Network Adapter"
TunDriver.SVCDESC = "Windows TUN Driver Service"

; Network adapter specific sections
[TunAdapter.NT]
Characteristics = 0x81 ; NCF_VIRTUAL | NCF_SOFTWARE_ENUMERATED
BusType = 15 ; PNPBus
AddReg = TunAdapter.Reg
CopyFiles = TunAdapter.CopyFiles

[TunAdapter.Reg]
HKR, Ndi, Service, 0, "TunDriver"
HKR, Ndi\Interfaces, UpperRange, 0, "ndis5"
HKR, Ndi\Interfaces, LowerRange, 0, "ethernet"

[TunAdapter.CopyFiles]
tun_driver.sys

[TunAdapter.NT.Services]
AddService = TunDriver, 2, TunAdapter.Service, TunAdapter.EventLog

[TunAdapter.Service]
DisplayName = %TunDriver.Service.DispName%
ServiceType = 1 ; SERVICE_KERNEL_DRIVER
StartType = 3 ; SERVICE_DEMAND_START
ErrorControl = 1 ; SERVICE_ERROR_NORMAL
ServiceBinary = %12%\tun_driver.sys
LoadOrderGroup = NDIS

[TunAdapter.EventLog]
AddReg = TunAdapter.AddEventLogReg

[TunAdapter.AddEventLogReg]
HKR, , EventMessageFile, 0x00020000, "%%SystemRoot%%\System32\netevent.dll"
HKR, , TypesSupported, 0x00010001, 7

; Installation sections for different architectures
[DefaultInstall.NT]
CopyFiles=Drivers_Dir

[DefaultInstall.NT.Services]
AddService = TunDriver,%SPSVCINST_ASSOCSERVICE%, TunDriver_Service_Inst

[DefaultUninstall.NT]
DelFiles=Drivers_Dir

[DefaultUninstall.NT.Services]
DelService = TunDriver,0x200

; String definitions for different languages
[Strings.0409] ; English (US)
ManufacturerName="Windows TUN Driver"
DiskName = "Windows TUN Driver Installation Disk"
TunDriver.DeviceDesc = "Windows TUN Virtual Network Adapter"
TunDriver.SVCDESC = "Windows TUN Driver Service"
TunDriver.Service.DispName = "Windows TUN Driver"

[Strings.0804] ; Chinese (Simplified)
ManufacturerName="Windows TUN 驱动程序"
DiskName = "Windows TUN 驱动程序安装盘"
TunDriver.DeviceDesc = "Windows TUN 虚拟网络适配器"
TunDriver.SVCDESC = "Windows TUN 驱动程序服务"
TunDriver.Service.DispName = "Windows TUN 驱动程序"
